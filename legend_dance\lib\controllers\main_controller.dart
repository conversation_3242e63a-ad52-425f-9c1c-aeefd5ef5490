import 'package:get/get.dart';
import 'package:logger/logger.dart';
import '../main.dart';

/// 主控制器，负责全局状态管理
class MainController extends GetxController {
  final Logger _logger = CustomApp.logger;

  // 全局状态
  final RxBool isLoading = false.obs;
  final RxString currentRoute = "".obs;
  final RxInt field_1f = 0.obs;
  final RxInt tabChanges = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _logger.i("MainController - 初始化完成");
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    isLoading.value = loading;
  }

  /// 设置当前路由
  void setCurrentRoute(String route) {
    currentRoute.value = route;
  }

  @override
  void onClose() {
    _logger.i("MainController - 控制器销毁");
    super.onClose();
  }
}
