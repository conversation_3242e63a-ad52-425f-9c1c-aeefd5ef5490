import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

import '../services/video_import_record_service.dart';

// 混合类定义
mixin GetTickerProviderStateMixin<T extends StatefulWidget> on GetxController
    implements TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) {
    return Ticker(onTick);
  }
}

// 基础控制器混合
abstract class _VideoDetailController extends GetxController {}

class VideoImportController extends _VideoDetailController
    with GetTickerProviderStateMixin {
  late final Logger _logger;
  late VideoImportRecordService _recordService;
  late final AnimationController fadeController;

  // 状态管理
  final RxBool isImporting = false.obs;
  final RxBool isDisposed = false.obs;
  final RxString currentStatus = "".obs;
  final RxString selectedVideoPath = "".obs;

  // 视频信息
  final RxDouble aspectRatio = (16.0 / 9.0).obs;
  final RxString aspectRatioDescription = "16:9".obs;
  final RxBool isStandardAspectRatioValue = true.obs;

  @override
  void onInit() {
    super.onInit();

    _logger = Logger();
    _logger.i("VideoImportController - onInit 开始初始化");

    _initRecordService();
    _initAnimationController();

    _logger.i("VideoImportController - onInit 初始化完成");
  }

  /// 初始化记录服务
  void _initRecordService() {
    try {
      if (Get.isRegistered<VideoImportRecordService>()) {
        _recordService = Get.find<VideoImportRecordService>();
      } else {
        _recordService = Get.put<VideoImportRecordService>(
          VideoImportRecordService(),
          permanent: true,
        );
      }
      _logger.i("视频导入记录服务初始化成功");
    } catch (e) {
      _logger.e("初始化视频导入记录服务时出错: $e");
      _recordService = Get.put<VideoImportRecordService>(
        VideoImportRecordService(),
        permanent: true,
      );
    }
  }

  /// 初始化动画控制器
  void _initAnimationController() {
    fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  /// 重置控制器状态
  void reset() {
    selectedVideoPath.value = "";
    aspectRatio.value = 16.0 / 9.0;
    aspectRatioDescription.value = "16:9";
    isStandardAspectRatioValue.value = true;
    isImporting.value = false;
    currentStatus.value = "";

    fadeController.reset();
  }

  /// 处理所有动画完成
  void handleAllAnimationsComplete() {
    _logger.i("所有动画已完成");
    fadeController.forward();
  }

  /// 获取宽高比描述
  String getAspectRatioDescription(double ratio) {
    // 常见宽高比判断
    if ((ratio - 16 / 9).abs() < 0.01) return "16:9";
    if ((ratio - 4 / 3).abs() < 0.01) return "4:3";
    if ((ratio - 1.0).abs() < 0.01) return "1:1";
    if ((ratio - 9 / 16).abs() < 0.01) return "9:16";

    // 返回计算出的比例
    return "${(ratio * 100).round() / 100}:1";
  }

  /// 判断是否为标准宽高比
  bool _isStandardAspectRatio(double ratio) {
    const standardRatios = [16 / 9, 4 / 3, 1.0, 9 / 16];
    return standardRatios.any((standard) => (ratio - standard).abs() < 0.01);
  }

  /// 设置销毁状态
  void setDisposed(bool disposed) {
    isDisposed.value = disposed;
  }

  /// 检查并重置状态
  Future<void> checkAndResetState() async {
    try {
      _logger.i("检查当前路由状态");

      final currentRoute = Get.currentRoute;
      final previousRoute = Get.previousRoute;

      _logger.i("当前路由: $currentRoute, 上一个路由: $previousRoute");

      // 根据路由状态进行相应的处理
      if (currentRoute.contains('/video_import')) {
        // 在视频导入页面，执行相关逻辑
        await _handleVideoImportRoute();
      }
    } catch (e) {
      _logger.e("检查并重置状态时出错: $e");
    }
  }

  /// 处理视频导入路由逻辑
  Future<void> _handleVideoImportRoute() async {
    // 这里实现具体的视频导入页面逻辑
    _logger.i("处理视频导入路由逻辑");
  }

  /// 选择视频文件
  Future<void> selectVideoFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        selectedVideoPath.value = filePath;
        _logger.i("选择视频文件: $filePath");

        // 这里可以添加视频信息分析逻辑
        await _analyzeVideoFile(file);
      } else {
        _logger.e("视频文件不存在: $filePath");
      }
    } catch (e) {
      _logger.e("选择视频文件时出错: $e");
    }
  }

  /// 分析视频文件
  Future<void> _analyzeVideoFile(File file) async {
    try {
      // 这里实现视频文件分析逻辑
      // 获取视频的宽高比、时长等信息
      _logger.i("开始分析视频文件: ${file.path}");

      // 临时设置，实际应该通过视频解析获取
      aspectRatio.value = 16.0 / 9.0;
      aspectRatioDescription.value = getAspectRatioDescription(
        aspectRatio.value,
      );
      isStandardAspectRatioValue.value = _isStandardAspectRatio(
        aspectRatio.value,
      );
    } catch (e) {
      _logger.e("分析视频文件时出错: $e");
    }
  }

  @override
  void onClose() {
    _logger.i("VideoImportController - 控制器销毁");
    fadeController.dispose();
    super.onClose();
  }
}
