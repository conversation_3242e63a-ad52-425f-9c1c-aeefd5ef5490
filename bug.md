I/flutter (31431): ════════════════════════════════════════════════════════════════════════════════════════════════════
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:46.580 (+0:00:01.139424)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: NoSuchMethodError: Class 'MyWorksController' has no instance getter 'isRestoringData'.
I/flutter (31431): │ ⛔ Receiver: Instance of 'MyWorksController'
I/flutter (31431): │ ⛔ Tried calling: isRestoringData
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): Another exception was thrown: "CreationController" not found. You need to call "Get.put(CreationController())" or "Get.lazyPut(()=>CreationController())"
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:46.644 (+0:00:01.204175)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: "CreationController" not found. You need to call "Get.put(CreationController())" or "Get.lazyPut(()=>CreationController())"
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): Another exception was thrown: A RenderViewport expected a child of type RenderSliver but received a child of type RenderErrorBox.
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:46.707 (+0:00:01.267236)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: A RenderViewport expected a child of type RenderSliver but received a child of type RenderErrorBox.
I/flutter (31431): │ ⛔ RenderObjects expect specific types of children because they coordinate with their children during layout and paint. For example, a RenderSliver cannot be the child of a RenderBox because a RenderSliver does not understand the RenderBox layout protocol.
I/flutter (31431): │ ⛔
I/flutter (31431): │ ⛔ The RenderViewport that expected a RenderSliver child was created by:
I/flutter (31431): │ ⛔   Viewport ← IgnorePointer-[GlobalKey#9cc85] ← Semantics ← Listener ← _GestureSemantics ← RawGestureDetector-[LabeledGlobalKey<RawGestureDetectorState>#89f8d] ← Listener ← _ScrollableScope ← _ScrollSemantics-[GlobalKey#4587e] ← NotificationListener<ScrollMetricsNotification> ← Transform ← ClipRect ← ⋯
I/flutter (31431): │ ⛔
I/flutter (31431): │ ⛔ The RenderErrorBox that did not match the expected child type was created by:
I/flutter (31431): │ ⛔   ErrorWidget-[#748d8] ← SliverFillRemaining ← Obx ← Viewport ← IgnorePointer-[GlobalKey#9cc85] ← Semantics ← Listener ← _GestureSemantics ← RawGestureDetector-[LabeledGlobalKey<RawGestureDetectorState>#89f8d] ← Listener ← _ScrollableScope ← _ScrollSemantics-[GlobalKey#4587e] ← ⋯
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): Another exception was thrown: A RenderViewport expected a child of type RenderSliver but received a child of type RenderErrorBox.
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:46.755 (+0:00:01.314361)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: A RenderViewport expected a child of type RenderSliver but received a child of type RenderErrorBox.
I/flutter (31431): │ ⛔ RenderObjects expect specific types of children because they coordinate with their children during layout and paint. For example, a RenderSliver cannot be the child of a RenderBox because a RenderSliver does not understand the RenderBox layout protocol.
I/flutter (31431): │ ⛔
I/flutter (31431): │ ⛔ The RenderViewport that expected a RenderSliver child was created by:
I/flutter (31431): │ ⛔   Viewport ← IgnorePointer-[GlobalKey#9cc85] ← Semantics ← Listener ← _GestureSemantics ← RawGestureDetector-[LabeledGlobalKey<RawGestureDetectorState>#89f8d] ← Listener ← _ScrollableScope ← _ScrollSemantics-[GlobalKey#4587e] ← NotificationListener<ScrollMetricsNotification> ← Transform ← ClipRect ← ⋯
I/flutter (31431): │ ⛔
I/flutter (31431): │ ⛔ The RenderErrorBox that did not match the expected child type was created by:
I/flutter (31431): │ ⛔   ErrorWidget-[#990c5] ← Obx ← Viewport ← IgnorePointer-[GlobalKey#9cc85] ← Semantics ← Listener ← _GestureSemantics ← RawGestureDetector-[LabeledGlobalKey<RawGestureDetectorState>#89f8d] ← Listener ← _ScrollableScope ← _ScrollSemantics-[GlobalKey#4587e] ← NotificationListener<ScrollMetricsNotification> ← ⋯
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): Another exception was thrown: NoSuchMethodError: Class 'MyWorksController' has no instance getter 'isRestoringData'.
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:46.854 (+0:00:01.413379)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: NoSuchMethodError: Class 'MyWorksController' has no instance getter 'isRestoringData'.
I/flutter (31431): │ ⛔ Receiver: Instance of 'MyWorksController'
I/flutter (31431): │ ⛔ Tried calling: isRestoringData
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] start to get views' rect, type = SCENE_WINDOW_FOCUS_CHANGE
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] windowModeType: 1
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] displayPoint: Point(1260, 2720)
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] windowModeType: 1
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] lazyMode:
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] current mode is full screen
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   _CreationViewState._setupPostFrameCallback.<anonymous closure> (package:keepdance/pages/creation/views/creation_view.dart:139:15)
I/flutter (31431): │ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 创作页面已加载
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService.refreshWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:201:13)
I/flutter (31431): │ #1   MyWorksController.refreshData (package:keepdance/pages/creation/controllers/my_works_controller.dart:384:51)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 开始快速刷新策略
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   _CreationViewState._setupPostFrameCallback.<anonymous closure> (package:keepdance/pages/creation/views/creation_view.dart:144:17)
I/flutter (31431): │ #1   SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 创作页面初始化时已主动刷新VIP状态
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocalVideoScoreService.ensureDatabaseInitialized (package:keepdance/services/local_video_score_service.dart:37:15)
I/flutter (31431): │ #1   MyWorksService._processLocalVideos (package:keepdance/pages/creation/services/my_works_service.dart:105:36)
I/flutter (31431): │ #2   MyWorksService.loadLocalWorks (package:keepdance/pages/creation/services/my_works_service.dart:91:20)
I/flutter (31431): │ #3   <asynchronous suspension>
I/flutter (31431): │ #4   CreationPreloadService.refreshWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:222:40)
I/flutter (31431): │ #5   <asynchronous suspension>
I/flutter (31431): │ #6   MyWorksController.refreshData (package:keepdance/pages/creation/controllers/my_works_controller.dart:384:29)
I/flutter (31431): │ #7   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.104 (+0:00:01.663686)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 数据库正在初始化中，等待完成...
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/Choreographer(31431): Skipped 90 frames! The application may be doing too much work on its main thread.
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService.loadWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:148:13)
I/flutter (31431): │ #1   MyWorksController._initializeWorksData (package:keepdance/pages/creation/controllers/my_works_controller.dart:202:51)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 开始离线优先加载策略
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService.loadWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:168:13)
I/flutter (31431): │ #1   MyWorksController._initializeWorksData (package:keepdance/pages/creation/controllers/my_works_controller.dart:202:51)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 预加载未完成，直接加载完整数据
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocalVideoScoreService.ensureDatabaseInitialized (package:keepdance/services/local_video_score_service.dart:37:15)
I/flutter (31431): │ #1   MyWorksService._processLocalVideos (package:keepdance/pages/creation/services/my_works_service.dart:105:36)
I/flutter (31431): │ #2   MyWorksService.loadLocalWorks (package:keepdance/pages/creation/services/my_works_service.dart:91:20)
I/flutter (31431): │ #3   <asynchronous suspension>
I/flutter (31431): │ #4   CreationPreloadService.loadWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:178:40)
I/flutter (31431): │ #5   <asynchronous suspension>
I/flutter (31431): │ #6   MyWorksController._initializeWorksData (package:keepdance/pages/creation/controllers/my_works_controller.dart:202:29)
I/flutter (31431): │ #7   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.117 (+0:00:01.677075)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 数据库正在初始化中，等待完成...
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   VipCacheService.clearVipInfo (package:keepdance/services/vip_cache_service.dart:224:17)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 VIP缓存信息已清除
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:188:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.130 (+0:00:01.689987)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ 全局错误: LateInitializationError: Field '_logger@92008013' has not been initialized.
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocalVideoScoreService._initDatabase (package:keepdance/services/local_video_score_service.dart:72:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): │ #2   LocalVideoScoreService.onInit (package:keepdance/services/local_video_score_service.dart:383:5)
I/flutter (31431): │ #3   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.132 (+0:00:01.691414)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 本地得分数据库初始化成功: /data/user/0/com.example.legend_dance/databases/local_video_scores.db
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocalVideoScoreService.onInit (package:keepdance/services/local_video_score_service.dart:384:13)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.132 (+0:00:01.692105)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 LocalVideoScoreService 初始化完成
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocalVideoScoreService._initDatabase (package:keepdance/services/local_video_score_service.dart:72:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): │ #2   LocalVideoScoreService.ensureDatabaseInitialized (package:keepdance/services/local_video_score_service.dart:48:7)
I/flutter (31431): │ #3   <asynchronous suspension>
I/flutter (31431): │ #4   CreationPreloadService._initializeDependencies.<anonymous closure> (package:keepdance/pages/creation/services/creation_preload_service.dart:91:19)
I/flutter (31431): │ #5   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.133 (+0:00:01.692686)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 本地得分数据库初始化成功: /data/user/0/com.example.legend_dance/databases/local_video_scores.db
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocalVideoScoreService.ensureDatabaseInitialized (package:keepdance/services/local_video_score_service.dart:49:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): │ #2   CreationPreloadService._initializeDependencies.<anonymous closure> (package:keepdance/pages/creation/services/creation_preload_service.dart:91:19)
I/flutter (31431): │ #3   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.134 (+0:00:01.693583)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 数据库初始化完成
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService._initializeDependencies.<anonymous closure> (package:keepdance/pages/creation/services/creation_preload_service.dart:92:23)
I/flutter (31431): │ #1   Future._propagateToListeners.handleValueCallback (dart:async/future_impl.dart:951:45)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 评分服务数据库异步初始化完成
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService._updateCache (package:keepdance/pages/creation/services/creation_preload_service.dart:306:13)
I/flutter (31431): │ #1   CreationPreloadService._loadCompleteData (package:keepdance/pages/creation/services/creation_preload_service.dart:280:5)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 缓存更新完成: 0 个作品，0 个分类
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService._loadCompleteData (package:keepdance/pages/creation/services/creation_preload_service.dart:285:13)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 完整数据加载完成: 0 个作品
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService._completePreload (package:keepdance/pages/creation/services/creation_preload_service.dart:261:13)
I/flutter (31431): │ #1   CreationPreloadService.startPrewarmLoading (package:keepdance/pages/creation/services/creation_preload_service.dart:136:7)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 预加载完成，缓存 0 个作品
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService._updateCache (package:keepdance/pages/creation/services/creation_preload_service.dart:306:13)
I/flutter (31431): │ #1   CreationPreloadService.refreshWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:223:7)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 缓存更新完成: 0 个作品，0 个分类
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService.refreshWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:224:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 快速刷新完成: 0 个作品
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   PaginationService.initializeData (package:keepdance/pages/creation/services/pagination_service.dart:118:13)
I/flutter (31431): │ #1   MyWorksController.refreshData (package:keepdance/pages/creation/controllers/my_works_controller.dart:391:32)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 初始化分页数据，总数据量: 0
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   PaginationService._applyFiltersAndReset (package:keepdance/pages/creation/services/pagination_service.dart:247:13)
I/flutter (31431): │ #1   PaginationService.initializeData (package:keepdance/pages/creation/services/pagination_service.dart:124:11)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 应用过滤器：搜索=""，分类=全部
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   PaginationService._applyFiltersAndReset (package:keepdance/pages/creation/services/pagination_service.dart:264:13)
I/flutter (31431): │ #1   PaginationService.initializeData (package:keepdance/pages/creation/services/pagination_service.dart:124:11)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 过滤后数据量: 0
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService._updateCache (package:keepdance/pages/creation/services/creation_preload_service.dart:306:13)
I/flutter (31431): │ #1   CreationPreloadService.loadWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:179:7)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 缓存更新完成: 0 个作品，0 个分类
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CreationPreloadService.loadWorksWithOfflineFirst (package:keepdance/pages/creation/services/creation_preload_service.dart:181:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 完整数据加载完成: 0 个作品
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:188:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.150 (+0:00:01.709641)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ 全局错误: LateInitializationError: Field '_logger@94140909' has not been initialized.
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/Gralloc4(31431): mapper 4.x is not supported
W/Gralloc3(31431): mapper 3.x is not supported
I/DecorView(31431): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
W/Gralloc4(31431): allocator 4.x is not supported
W/Gralloc3(31431): allocator 3.x is not supported
I/flutter (31431): Another exception was thrown: 'package:flutter/src/widgets/framework.dart': Failed assertion: line 7000 pos 12: 'child == _child': is not true.
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.172 (+0:00:01.731772)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: 'package:flutter/src/widgets/framework.dart': Failed assertion: line 7000 pos 12: 'child == _child': is not true.
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): Another exception was thrown: NoSuchMethodError: Class 'MyWorksController' has no instance getter 'isRestoringData'.
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.197 (+0:00:01.757236)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: NoSuchMethodError: Class 'MyWorksController' has no instance getter 'isRestoringData'.
I/flutter (31431): │ ⛔ Receiver: Instance of 'MyWorksController'
I/flutter (31431): │ ⛔ Tried calling: isRestoringData
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): Another exception was thrown: Duplicate GlobalKey detected in widget tree.
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:47.226 (+0:00:01.786306)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: Duplicate GlobalKey detected in widget tree.
I/flutter (31431): │ ⛔ The following GlobalKey was specified multiple times in the widget tree. This will lead to parts of the widget tree being truncated unexpectedly, because the second time a key is seen, the previous instance is moved to the new location. The key was:
I/flutter (31431): │ ⛔ - [GlobalKey#9cc85]
I/flutter (31431): │ ⛔ This was determined by noticing that after the widget with the above global key was moved out of its previous parent, that previous parent never updated during this frame, meaning that it either did not update at all or updated before the widget was moved, in either case implying that it still thinks that it should have a child with that global key.
I/flutter (31431): │ ⛔ The specific parent that did not update after having one or more children forcibly removed due to GlobalKey reparenting is:
I/flutter (31431): │ ⛔ - Semantics(container: false, properties: SemanticsProperties, renderObject: RenderSemanticsAnnotations#b36d0 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE DETACHED)
I/flutter (31431): │ ⛔ A GlobalKey can only be specified on one widget at a time in the widget tree.
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   HomeController._initializeAsync (package:keepdance/pages/home/<USER>/home_controller.dart:152:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ HomeController异步初始化失败: Could not find a generator for route RouteSettings("/login", null) in the _WidgetsAppState.
I/flutter (31431): │ ⛔ Make sure your root app widget has provided a way to generate
I/flutter (31431): │ ⛔ this route.
I/flutter (31431): │ ⛔ Generators for routes are searched for in the following order:
I/flutter (31431): │ ⛔  1. For the "/" route, the "home" property, if non-null, is used.
I/flutter (31431): │ ⛔  2. Otherwise, the "routes" table is used, if it has an entry for the route.
I/flutter (31431): │ ⛔  3. Otherwise, onGenerateRoute is called. It should return a non-null value for any valid route not handled by "home" and "routes".
I/flutter (31431): │ ⛔  4. Finally if all else fails onUnknownRoute is called.
I/flutter (31431): │ ⛔ Unfortunately, onUnknownRoute was not set.
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/DecorView(31431): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
I/DecorView[](31431): pkgName:com.example.legend_dance old windowMode:1 new windoMode:1, isFixedSize:false, isStackNeedCaptionView:true
I/DecorView(31431): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
I/DecorView(31431): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
I/HwViewRootImpl(31431): removeInvalidNode jank list is null
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   BaseAnalytics.trackEventSafely (package:keepdance/utils/analytics/base_analytics.dart:107:15)
I/flutter (31431): │ #1   PageAnalytics._trackPageView (package:keepdance/utils/analytics/page_analytics.dart:60:21)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ! BaseAnalytics 未初始化，正在初始化...
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] start to getViewHierarchy
I/AwareBitmapCacher(31431): init lrucache size: 4194304 pid=31431
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] deviceOrientation: 0
D/HwViewRootImpl(31431): [DetectViewsLocationRunner-ScreenDirection] ROTATION_0
D/HwViewRootImpl(31431): [DetectViewsLocationRunner] get views' rect = 0, SCENE_WINDOW_FOCUS_CHANGE
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   BaseAnalytics._initChannelInfo (package:keepdance/utils/analytics/base_analytics.dart:342:17)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 等待渠道信息初始化...
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   _CreationViewState._setupPostFrameCallback.<anonymous closure>.<anonymous closure>.<anonymous closure> (package:keepdance/pages/creation/views/creation_view.dart:159:21)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 A→B跳转修复：延迟刷新VIP状态完成，canDeleteWorks=false
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/ProfileInstaller(31431): Installing profile for com.example.legend_dance
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   BaseAnalytics._initChannelInfo (package:keepdance/utils/analytics/base_analytics.dart:352:17)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ! 未能获取渠道信息，使用默认值: unknown
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocationService._fetchAndSaveLocation (package:keepdance/utils/location_service.dart:87:15)
I/flutter (31431): │ #1   LocationService.getLocation (package:keepdance/utils/location_service.dart:69:22)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 开始请求位置信息API...
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   DeviceInfoService.getDeviceUniqueId (package:keepdance/services/device_info_service.dart:80:15)
I/flutter (31431): │ #1   ApiClient.getHeaders (package:keepdance/core/network/api_client.dart:302:42)
I/flutter (31431): │ #2   <asynchronous suspension>
I/flutter (31431): │ #3   ApiClient.getEnhanced (package:keepdance/core/network/api_client.dart:130:23)
I/flutter (31431): │ #4   <asynchronous suspension>
I/flutter (31431): │ #5   ApiClient.get (package:keepdance/core/network/api_client.dart:109:30)
I/flutter (31431): │ #6   <asynchronous suspension>
I/flutter (31431): │ #7   LocationService._fetchAndSaveLocation (package:keepdance/utils/location_service.dart:90:24)
I/flutter (31431): │ #8   <asynchronous suspension>
I/flutter (31431): │ #9   LocationService.getLocation (package:keepdance/utils/location_service.dart:69:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:05:52.451 (+0:00:07.011316)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ 获取设备唯一标识符失败: "PrivacyComplianceService" not found. You need to call "Get.put(PrivacyComplianceService())" or "Get.lazyPut(()=>PrivacyComplianceService())"
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocationService._fetchAndSaveLocation (package:keepdance/utils/location_service.dart:91:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 API响应数据: {code: 0, data: {status: 1, info: OK, infocode: 10000, country: 中国, province: 福建省, city: 厦门市, district: 海沧区, isp: 移动, location: 118.11022,24.490474, ip: ************}, msg: ok, error_type: null, error_details: null, http_status_code: 200}
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   LocationService._saveToCache (package:keepdance/utils/location_service.dart:137:13)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 💡 位置信息已保存到缓存: 中国, 福建省, 厦门市
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   BaseAnalytics.updateLocationInfo (package:keepdance/utils/analytics/base_analytics.dart:288:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 位置信息已更新
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   BaseAnalytics.init (package:keepdance/utils/analytics/base_analytics.dart:260:15)
I/flutter (31431): │ #1   <asynchronous suspension>
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 🐛 BaseAnalytics 初始化完成
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/ConnectivityManager(31431): requestNetwork and the calling app is: com.example.legend_dance
I/InputMethodManager(31431): Starting input: reason=DEACTIVATED_BY_IMMS
W/HwRemoteInputMethodManager(31431): isCasting false because IHwDistributedWindowManager is invalid.
I/InputMethodManager(31431): Starting input: Bind resultString=SUCCESS_WITH_IME_SESSION
I/LifecycleTransaction(31431): activityCallbacks TopResumedActivityChangeItem{onTop=false}
I/TopResumedActivityChangeItem(31431): execute start, ActivityClientRecord = ActivityRecord{d4961b4 token=android.os.BinderProxy@2ada156 {com.example.legend_dance/com.example.legend_dance.MainActivity}}
I/LifecycleTransaction(31431): lifecycleStateRequest PauseActivityItem{finished=false,userLeaving=false,configChanges=0,dontReport=false}
I/HwViewRootImpl(31431): remove sceneId 10 topId: 0
I/flutter (31431): Another exception was thrown: FormatException: Message corrupted
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:06:10.078 (+0:00:24.637737)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: FormatException: Message corrupted
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/LifecycleTransaction(31431): lifecycleStateRequest StopActivityItem{showWindow=false,configChanges=0}
I/ZrHung.AppEyeUiProbe(31431): not watching, wait.
W/BufferQueueProducer(31431): [SurfaceView[com.example.legend_dance/com.example.legend_dance.MainActivity]#1(BLAST Consumer)1](id:7ac700000001,api:0,p:-1,c:31431) disconnect: not connected (req=1)
W/libEGL  (31431): EGLNativeWindowType 0x7d428e9810 disconnect failed
W/HwApsImpl(31431): not support full screen video recognize feature  ## com.example.legend_dance
W/BufferQueueProducer(31431): [com.example.legend_dance/com.example.legend_dance.MainActivity#0(BLAST Consumer)0](id:7ac700000000,api:0,p:-1,c:31431) disconnect: not connected (req=1)
W/libEGL  (31431): EGLNativeWindowType 0x7d4287a010 disconnect failed
W/SurfaceView(31431): ViewUI notifySurfaceDestroyed
W/SurfaceView(31431): ViewUI notifySurfaceDestroyed
I/HwViewRootImpl(31431): Add sceneId 10 topId: 0
I/HwViewRootImpl(31431): Add sceneId 4 topId: 10
I/WindowManager(31431): trimMemory level: 20
I/HwViewRootImpl(31431): remove sceneId 4 topId: 10
I/HwViewRootImpl(31431): remove sceneId 10 topId: 0
W/SurfaceView(31431): ViewUI notifySurfaceDestroyed
I/flutter (31431): Another exception was thrown: FormatException: Message corrupted
I/flutter (31431): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31431): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ 17:06:10.472 (+0:00:25.031728)
I/flutter (31431): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31431): │ ⛔ Flutter错误: FormatException: Message corrupted
I/flutter (31431): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
W/SurfaceView(31431): ViewUI notifySurfaceDestroyed
I/InsetsSourceConsumer(31431): ViewUI_insets mSourceControl is null notifyControlRevoked type: 0