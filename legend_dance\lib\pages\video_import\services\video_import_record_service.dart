import 'package:get/get.dart';

/// 视频记录数据模型
class VideoRecord {
  final String filePath;
  final String fileHash;
  final int fileSize;
  final DateTime importDate;
  final bool isFullyProcessed;
  final String fileName;

  VideoRecord({
    required this.filePath,
    required this.fileHash,
    required this.fileSize,
    required this.importDate,
    required this.isFullyProcessed,
    required this.fileName,
  });

  /// 从JSON创建VideoRecord实例
  factory VideoRecord.fromJson(Map<String, dynamic> json) {
    return VideoRecord(
      filePath: json['filePath'] as String,
      fileHash: json['fileHash'] as String,
      fileSize: json['fileSize'] as int,
      importDate: DateTime.parse(json['importDate'] as String),
      isFullyProcessed: json['isFullyProcessed'] as bool,
      fileName: json['fileName'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'filePath': filePath,
      'fileHash': fileHash,
      'fileSize': fileSize,
      'importDate': importDate.toIso8601String(),
      'isFullyProcessed': isFullyProcessed,
      'fileName': fileName,
    };
  }
}

/// 视频导入记录服务
class VideoImportRecordService extends GetxService {
  final List<VideoRecord> _records = <VideoRecord>[];

  /// 获取所有视频记录
  List<VideoRecord> get records => List.unmodifiable(_records);

  /// 添加视频记录
  void addRecord(VideoRecord record) {
    _records.add(record);
  }

  /// 根据文件路径查找记录
  VideoRecord? findByFilePath(String filePath) {
    try {
      return _records.firstWhere((record) => record.filePath == filePath);
    } catch (e) {
      return null;
    }
  }

  /// 根据文件哈希查找记录
  VideoRecord? findByFileHash(String fileHash) {
    try {
      return _records.firstWhere((record) => record.fileHash == fileHash);
    } catch (e) {
      return null;
    }
  }

  /// 移除记录
  bool removeRecord(String filePath) {
    final index = _records.indexWhere((record) => record.filePath == filePath);
    if (index != -1) {
      _records.removeAt(index);
      return true;
    }
    return false;
  }

  /// 清空所有记录
  void clearAll() {
    _records.clear();
  }

  /// 获取记录总数
  int get recordCount => _records.length;

  /// 检查文件是否已导入（通过文件哈希）
  bool isFileImported(String fileHash) {
    return _records.any((record) => record.fileHash == fileHash);
  }

  /// 计算文件哈希值
  Future<String?> calculateFileHash(String path) async {
    // 这里应该实现实际的文件哈希计算逻辑
    // 目前返回null作为占位符
    return null;
  }

  /// 根据路径和哈希移除视频记录
  Future<bool> removeVideoRecord(String path, String? hash) async {
    return removeRecord(path);
  }

  /// 根据哈希移除视频记录
  Future<bool> removeVideoRecordByHash(String hash) async {
    final index = _records.indexWhere((record) => record.fileHash == hash);
    if (index != -1) {
      _records.removeAt(index);
      return true;
    }
    return false;
  }

  /// 根据部分匹配移除视频记录
  Future<int> removeVideoRecordsByPartialMatch(
    List<String> partialPaths,
  ) async {
    int removedCount = 0;
    _records.removeWhere((record) {
      for (final partial in partialPaths) {
        if (record.filePath.contains(partial)) {
          removedCount++;
          return true;
        }
      }
      return false;
    });
    return removedCount;
  }

  /// 移除不存在的视频记录
  Future<void> removeNonExistentVideoRecords() async {
    // 这里应该检查文件是否存在并移除不存在的记录
    // 目前为空实现
  }

  /// 保存导入的视频
  Future<void> saveImportedVideos() async {
    // 这里应该实现保存逻辑
    // 目前为空实现
  }
}
