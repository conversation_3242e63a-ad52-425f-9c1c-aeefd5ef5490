-- Merging decision tree log ---
application
INJECTED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-47:19
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-47:19
MERGED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8de4b11941e9ac0570a9ba3df35f200\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8de4b11941e9ac0570a9ba3df35f200\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a57044d90e87c68614d167bac9b8275a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a57044d90e87c68614d167bac9b8275a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:device_info_plus] D:\project\ai-dance\legend_dance\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-49:12
MERGED from [:wakelock_plus] D:\project\ai-dance\legend_dance\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] D:\project\ai-dance\legend_dance\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] D:\project\ai-dance\legend_dance\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\f58a2d7f5ffcd1bae3d42bea5bf63dcd\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\72ec85942d6f0c14fb85995929849a07\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8de4b11941e9ac0570a9ba3df35f200\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:17:1-36:12
MERGED from [:connectivity_plus] D:\project\ai-dance\legend_dance\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_plugin_android_lifecycle] D:\project\ai-dance\legend_dance\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] D:\project\ai-dance\legend_dance\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] D:\project\ai-dance\legend_dance\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:pose_landmarker_plugin] D:\project\ai-dance\legend_dance\build\pose_landmarker_plugin\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] D:\project\ai-dance\legend_dance\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:video_player_android] D:\project\ai-dance\legend_dance\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:video_thumbnail] D:\project\ai-dance\legend_dance\build\video_thumbnail\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\744ef0616acd167077bd442bba141275\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4c7ccc4495020ec151e51318a1fa1ad\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bba37c8e9c6495f9eb26a76e34301ba\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4c05eaffed710d6c61f97a8b8ac890\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d9c1fb34a3a736a29af9203bd1ccf06\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2fbf221633a11202a49e7e11d793bf1\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e5757bbf3b5c6f98be61ba84aed2ced\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f33b78ec8f59eb03bf3d68a271f2a49\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09053c49aebe6b54a556d88a30a174ab\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\669dcb043dfd5b8ba3c17c25f2994f66\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d7ae9f044efe0bd8492f96588cc2ce8\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\322c68e59856509d4727dd3729f949bc\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\661cfed71eb01d7f0c4bbaeae6faab4d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d3b67c81f17a9d01acb41ef7dc29fd\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e82a987f49d0f6484c5e02710942378\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc9487afb3090e92844f4382c0195afd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51aab5cd0fa4197e7c50f04a365e85eb\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6a04692cb28023b65a80b358730356b\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-extractor:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3101b037d5b1dd58957009fe34e5eb67\transformed\jetified-media3-extractor-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\28cd1485137c4775be03f4a6b97daed8\transformed\jetified-media3-container-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6be7fa38867d1a8633f4b02b0db9ed89\transformed\jetified-media3-datasource-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0346041808a057281e063c0f2adc0e93\transformed\jetified-media3-decoder-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4bba53a3533cb3de07828c12928e94d7\transformed\jetified-media3-database-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\837c76a2f3d1d18c5eaed90d79b785fe\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72bc5a5cbd46bf43685ccd950c0a6972\transformed\jetified-media3-exoplayer-hls-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a755a5354033bead7bb511b42096091a\transformed\jetified-media3-exoplayer-dash-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbda523fc50f0129dc57460df2ed4be\transformed\jetified-media3-exoplayer-rtsp-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\766204c00a7a208fb76e45884b4e7941\transformed\jetified-media3-exoplayer-smoothstreaming-1.5.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\06df5b0a8a4ae99dc00341f09e7e943f\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f568acae4ccb53cd32952f50f20d1ff2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88ea6bbee0c3d252da1a135eb4f3ca19\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b073446a44c5a5ccccc7d6163478c48\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4f2357047f2fdd854a789973139f996\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b668b148cca3808fa3f3cd347beb72bd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee991f1e2a54faf463391e906c8be955\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b8044963892eeec1eade62cd96c4f00\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9bf0628666be1a9ac10b49d28bb02595\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b885d5fed361a7d9dac4e4d02dd1b62a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ce1623fa95b61c6893b88d5514e5342\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0eba1e0f5a4cc889e5e35448a916e4c0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\14ea3873c21d7fdac7c95d2a65c717f3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fed144bef681fc8fb80134095669b372\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf782f0e0913fb55b1494a7adbb5fe28\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\274b962bfc0ee3499460b07125b40653\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [id.zelory:compressor:3.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\15910b642dde62d81022d8a2ace5c90a\transformed\jetified-compressor-3.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c00c34f73466195bccbeb63410c0399\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\749ed3f27cabf52b3ef491eaf357cdb7\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d63b0fb941c9abc7885c3e08b0f44f6\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbe28563875c84dd3083b92f88ac4e75\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dcb95f8119ec5b1845af3693aa8a7063\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60422c0d8dee1aa8ed60043a80195e1a\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9724ff6e8d4e6c9244bfb7005fae21c2\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a57044d90e87c68614d167bac9b8275a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d94238d4ead240e85cffc1b6b569202f\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54366e878a08f39217fdd8161987674f\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b61f1bab78087bfbc995eb0d5aec9215\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40dfcd36206381321d61bc4d0a504790\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b148f6a8d5614a638ebe4c9eaa4cb54\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31c416e9a0d8e87bca40b3df9f863f70\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4cc418d3ecaefc3f424b95e02cf7e04\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.tencent.mm.opensdk:wechat-sdk-android:6.8.34] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7b0a9857f9ba0dea4e80abbe4776a9a\transformed\jetified-wechat-sdk-android-6.8.34\AndroidManifest.xml:2:1-9:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ebb3aa7827752119b2d0074c8f4eeaa\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:39:5-44:15
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-14:15
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-14:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml:6:5-66
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
	android:name
		ADDED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
MERGED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] D:\project\ai-dance\legend_dance\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] D:\project\ai-dance\legend_dance\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] D:\project\ai-dance\legend_dance\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] D:\project\ai-dance\legend_dance\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] D:\project\ai-dance\legend_dance\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] D:\project\ai-dance\legend_dance\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\project\ai-dance\legend_dance\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\project\ai-dance\legend_dance\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\f58a2d7f5ffcd1bae3d42bea5bf63dcd\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\f58a2d7f5ffcd1bae3d42bea5bf63dcd\transformed\jetified-camera-video-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\72ec85942d6f0c14fb85995929849a07\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\72ec85942d6f0c14fb85995929849a07\transformed\jetified-camera-lifecycle-1.5.0-beta01\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8de4b11941e9ac0570a9ba3df35f200\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8de4b11941e9ac0570a9ba3df35f200\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:21:5-44
MERGED from [:connectivity_plus] D:\project\ai-dance\legend_dance\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] D:\project\ai-dance\legend_dance\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\project\ai-dance\legend_dance\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\project\ai-dance\legend_dance\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\project\ai-dance\legend_dance\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\project\ai-dance\legend_dance\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\project\ai-dance\legend_dance\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\project\ai-dance\legend_dance\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:pose_landmarker_plugin] D:\project\ai-dance\legend_dance\build\pose_landmarker_plugin\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:pose_landmarker_plugin] D:\project\ai-dance\legend_dance\build\pose_landmarker_plugin\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] D:\project\ai-dance\legend_dance\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] D:\project\ai-dance\legend_dance\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] D:\project\ai-dance\legend_dance\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] D:\project\ai-dance\legend_dance\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_thumbnail] D:\project\ai-dance\legend_dance\build\video_thumbnail\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_thumbnail] D:\project\ai-dance\legend_dance\build\video_thumbnail\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\744ef0616acd167077bd442bba141275\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\744ef0616acd167077bd442bba141275\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4c7ccc4495020ec151e51318a1fa1ad\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b4c7ccc4495020ec151e51318a1fa1ad\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bba37c8e9c6495f9eb26a76e34301ba\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bba37c8e9c6495f9eb26a76e34301ba\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4c05eaffed710d6c61f97a8b8ac890\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4c05eaffed710d6c61f97a8b8ac890\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d9c1fb34a3a736a29af9203bd1ccf06\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5d9c1fb34a3a736a29af9203bd1ccf06\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2fbf221633a11202a49e7e11d793bf1\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e2fbf221633a11202a49e7e11d793bf1\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e5757bbf3b5c6f98be61ba84aed2ced\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e5757bbf3b5c6f98be61ba84aed2ced\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f33b78ec8f59eb03bf3d68a271f2a49\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f33b78ec8f59eb03bf3d68a271f2a49\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09053c49aebe6b54a556d88a30a174ab\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\09053c49aebe6b54a556d88a30a174ab\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\669dcb043dfd5b8ba3c17c25f2994f66\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\669dcb043dfd5b8ba3c17c25f2994f66\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d7ae9f044efe0bd8492f96588cc2ce8\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0d7ae9f044efe0bd8492f96588cc2ce8\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\322c68e59856509d4727dd3729f949bc\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\322c68e59856509d4727dd3729f949bc\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\661cfed71eb01d7f0c4bbaeae6faab4d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\661cfed71eb01d7f0c4bbaeae6faab4d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d3b67c81f17a9d01acb41ef7dc29fd\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d3b67c81f17a9d01acb41ef7dc29fd\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e82a987f49d0f6484c5e02710942378\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e82a987f49d0f6484c5e02710942378\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc9487afb3090e92844f4382c0195afd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc9487afb3090e92844f4382c0195afd\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51aab5cd0fa4197e7c50f04a365e85eb\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51aab5cd0fa4197e7c50f04a365e85eb\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6a04692cb28023b65a80b358730356b\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d6a04692cb28023b65a80b358730356b\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-extractor:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3101b037d5b1dd58957009fe34e5eb67\transformed\jetified-media3-extractor-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3101b037d5b1dd58957009fe34e5eb67\transformed\jetified-media3-extractor-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\28cd1485137c4775be03f4a6b97daed8\transformed\jetified-media3-container-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\28cd1485137c4775be03f4a6b97daed8\transformed\jetified-media3-container-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6be7fa38867d1a8633f4b02b0db9ed89\transformed\jetified-media3-datasource-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6be7fa38867d1a8633f4b02b0db9ed89\transformed\jetified-media3-datasource-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0346041808a057281e063c0f2adc0e93\transformed\jetified-media3-decoder-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0346041808a057281e063c0f2adc0e93\transformed\jetified-media3-decoder-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4bba53a3533cb3de07828c12928e94d7\transformed\jetified-media3-database-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4bba53a3533cb3de07828c12928e94d7\transformed\jetified-media3-database-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\837c76a2f3d1d18c5eaed90d79b785fe\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\837c76a2f3d1d18c5eaed90d79b785fe\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72bc5a5cbd46bf43685ccd950c0a6972\transformed\jetified-media3-exoplayer-hls-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\72bc5a5cbd46bf43685ccd950c0a6972\transformed\jetified-media3-exoplayer-hls-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a755a5354033bead7bb511b42096091a\transformed\jetified-media3-exoplayer-dash-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a755a5354033bead7bb511b42096091a\transformed\jetified-media3-exoplayer-dash-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbda523fc50f0129dc57460df2ed4be\transformed\jetified-media3-exoplayer-rtsp-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fbda523fc50f0129dc57460df2ed4be\transformed\jetified-media3-exoplayer-rtsp-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\766204c00a7a208fb76e45884b4e7941\transformed\jetified-media3-exoplayer-smoothstreaming-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\766204c00a7a208fb76e45884b4e7941\transformed\jetified-media3-exoplayer-smoothstreaming-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\06df5b0a8a4ae99dc00341f09e7e943f\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\06df5b0a8a4ae99dc00341f09e7e943f\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f568acae4ccb53cd32952f50f20d1ff2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f568acae4ccb53cd32952f50f20d1ff2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88ea6bbee0c3d252da1a135eb4f3ca19\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88ea6bbee0c3d252da1a135eb4f3ca19\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b073446a44c5a5ccccc7d6163478c48\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b073446a44c5a5ccccc7d6163478c48\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4f2357047f2fdd854a789973139f996\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4f2357047f2fdd854a789973139f996\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b668b148cca3808fa3f3cd347beb72bd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b668b148cca3808fa3f3cd347beb72bd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee991f1e2a54faf463391e906c8be955\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee991f1e2a54faf463391e906c8be955\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b8044963892eeec1eade62cd96c4f00\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b8044963892eeec1eade62cd96c4f00\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9bf0628666be1a9ac10b49d28bb02595\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\9bf0628666be1a9ac10b49d28bb02595\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b885d5fed361a7d9dac4e4d02dd1b62a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b885d5fed361a7d9dac4e4d02dd1b62a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ce1623fa95b61c6893b88d5514e5342\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ce1623fa95b61c6893b88d5514e5342\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0eba1e0f5a4cc889e5e35448a916e4c0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0eba1e0f5a4cc889e5e35448a916e4c0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\14ea3873c21d7fdac7c95d2a65c717f3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\14ea3873c21d7fdac7c95d2a65c717f3\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fed144bef681fc8fb80134095669b372\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fed144bef681fc8fb80134095669b372\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf782f0e0913fb55b1494a7adbb5fe28\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf782f0e0913fb55b1494a7adbb5fe28\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\274b962bfc0ee3499460b07125b40653\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\274b962bfc0ee3499460b07125b40653\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [id.zelory:compressor:3.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\15910b642dde62d81022d8a2ace5c90a\transformed\jetified-compressor-3.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [id.zelory:compressor:3.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\15910b642dde62d81022d8a2ace5c90a\transformed\jetified-compressor-3.0.1\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c00c34f73466195bccbeb63410c0399\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c00c34f73466195bccbeb63410c0399\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\749ed3f27cabf52b3ef491eaf357cdb7\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\749ed3f27cabf52b3ef491eaf357cdb7\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d63b0fb941c9abc7885c3e08b0f44f6\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3d63b0fb941c9abc7885c3e08b0f44f6\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbe28563875c84dd3083b92f88ac4e75\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\fbe28563875c84dd3083b92f88ac4e75\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dcb95f8119ec5b1845af3693aa8a7063\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dcb95f8119ec5b1845af3693aa8a7063\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60422c0d8dee1aa8ed60043a80195e1a\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\60422c0d8dee1aa8ed60043a80195e1a\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9724ff6e8d4e6c9244bfb7005fae21c2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9724ff6e8d4e6c9244bfb7005fae21c2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a57044d90e87c68614d167bac9b8275a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a57044d90e87c68614d167bac9b8275a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d94238d4ead240e85cffc1b6b569202f\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\d94238d4ead240e85cffc1b6b569202f\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54366e878a08f39217fdd8161987674f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54366e878a08f39217fdd8161987674f\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b61f1bab78087bfbc995eb0d5aec9215\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b61f1bab78087bfbc995eb0d5aec9215\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40dfcd36206381321d61bc4d0a504790\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\40dfcd36206381321d61bc4d0a504790\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b148f6a8d5614a638ebe4c9eaa4cb54\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b148f6a8d5614a638ebe4c9eaa4cb54\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31c416e9a0d8e87bca40b3df9f863f70\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\31c416e9a0d8e87bca40b3df9f863f70\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4cc418d3ecaefc3f424b95e02cf7e04\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4cc418d3ecaefc3f424b95e02cf7e04\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.tencent.mm.opensdk:wechat-sdk-android:6.8.34] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7b0a9857f9ba0dea4e80abbe4776a9a\transformed\jetified-wechat-sdk-android-6.8.34\AndroidManifest.xml:5:5-7:41
MERGED from [com.tencent.mm.opensdk:wechat-sdk-android:6.8.34] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7b0a9857f9ba0dea4e80abbe4776a9a\transformed\jetified-wechat-sdk-android-6.8.34\AndroidManifest.xml:5:5-7:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ebb3aa7827752119b2d0074c8f4eeaa\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\6ebb3aa7827752119b2d0074c8f4eeaa\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml
uses-feature#android.hardware.camera.any
ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
	android:name
		ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
uses-permission#android.permission.CAMERA
ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
	android:name
		ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
	android:name
		ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
	android:maxSdkVersion
		ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
	android:name
		ADDED from [:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-65
uses-permission#android.permission.READ_EXTERNAL_STORAGE
IMPLIED from D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml:1:1-7:12 reason: io.flutter.plugins.camerax requested WRITE_EXTERNAL_STORAGE
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:connectivity_plus] D:\project\ai-dance\legend_dance\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] D:\project\ai-dance\legend_dance\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\837c76a2f3d1d18c5eaed90d79b785fe\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\837c76a2f3d1d18c5eaed90d79b785fe\transformed\jetified-media3-common-1.5.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\06df5b0a8a4ae99dc00341f09e7e943f\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\06df5b0a8a4ae99dc00341f09e7e943f\transformed\jetified-media3-exoplayer-1.5.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-76
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-73
package#com.tencent.mm
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-50
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:18-47
activity#com.jarvan.fluwx.wxapi.FluwxWXEntryActivity
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-22:58
	android:launchMode
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-44
	android:exported
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-37
	android:theme
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-55
	android:taskAffinity
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-52
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-71
activity-alias#${applicationId}.wxapi.WXPayEntryActivity
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-29:58
	android:launchMode
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-48
	android:exported
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-36
	android:targetActivity
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-81
	android:theme
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-55
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-69
activity-alias#com.example.legend_dance.wxapi.WXPayEntryActivity
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-29:58
	android:launchMode
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-48
	android:exported
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-36
	android:targetActivity
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-81
	android:theme
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-55
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-69
activity-alias#${applicationId}.wxapi.WXEntryActivity
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-36:58
	android:launchMode
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-43
	android:exported
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-36
	android:targetActivity
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-81
	android:theme
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-55
	android:taskAffinity
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-52
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-66
activity-alias#com.example.legend_dance.wxapi.WXEntryActivity
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-36:58
	android:launchMode
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-43
	android:exported
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-36
	android:targetActivity
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-81
	android:theme
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-55
	android:taskAffinity
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-52
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-66
provider#com.jarvan.fluwx.FluwxFileProvider
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-46:20
	android:grantUriPermissions
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-47
	android:authorities
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-65
	android:exported
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-45:69
	android:resource
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:17-66
	android:name
		ADDED from [:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:17-67
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8de4b11941e9ac0570a9ba3df35f200\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\b8de4b11941e9ac0570a9ba3df35f200\transformed\jetified-camera-core-1.5.0-beta01\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.legend_dance.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.legend_dance.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
