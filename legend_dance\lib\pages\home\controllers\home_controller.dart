// lib: , url: package:keepdance/pages/home/<USER>/home_controller.dart
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:keepdance/core/network/api_client.dart';
import 'package:keepdance/models/dance_history.dart';
import 'package:keepdance/models/dance_theme.dart';
import 'package:keepdance/models/dance_type.dart';
import 'package:keepdance/models/user.dart';
import 'package:keepdance/models/dance_material.dart';
import 'package:keepdance/pages/home/<USER>/just_dance_category_service.dart';
import 'package:keepdance/services/cache_service.dart';
import 'package:keepdance/services/dance_history_service.dart';
import 'package:keepdance/services/dance_theme_service.dart';
import 'package:keepdance/services/error_classification_service.dart';
import 'package:keepdance/services/vip_cache_service.dart';
import 'package:keepdance/utils/analytics/base_analytics.dart';
import 'package:keepdance/utils/storage/global_data.dart';
import 'package:logger/logger.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:keepdance/common_widgets/common_tips.dart'; // 假设存在这个公共提示组件


// 根据汇编代码推断出的类定义
class TypeItemDataChild {
  final int? id;
  final String? name;

  const TypeItemDataChild({this.id, this.name});
}

class TypeItemData {
  final int? id;
  final String? name;
  final RxList<TypeItemDataChild> children;

  const TypeItemData({this.id, this.name, required this.children});
}


class HomeController extends GetxController {
  //============================================================
  // 静态字段 (Static Fields)
  //============================================================
  static late final Logger _logger;

  //============================================================
  // 服务依赖 (Services & Dependencies)
  //============================================================
  late final SharedPreferences _prefs;
  late final ErrorClassificationService _errorClassificationService;
  late final JustDanceCategoryService _justDanceCategoryService;
  final CacheService _cacheService = Get.find<CacheService>();
  final DanceThemeService _danceThemeService = DanceThemeService();
  final DanceHistoryService _danceHistoryService = DanceHistoryService();
  final ApiClient _apiClient = Get.find<ApiClient>();

  //============================================================
  // UI 状态 (UI State)
  //============================================================
  final RxInt tabIndex = 0.obs;
  
  // 创作标签页索引常量
  int get creationTabIndex => 1; // 假设创作页在第二个标签位置
  final RxBool isThemeLoading = false.obs;
  final RxList<DanceTheme> danceThemeList = <DanceTheme>[].obs;
  final RxList<DanceType> justDanceTypeList = <DanceType>[].obs;
  final RxInt selectedDanceTypeId = 0.obs;
  final RefreshController refreshController = RefreshController();
  final Rx<User?> userInfo = Rx<User?>(null);
  final RxList<DanceHistory> danceHistoryList = <DanceHistory>[].obs;
  final RxBool isHistoryLoading = false.obs;
  final RxInt historyPage = 1.obs;
  final RxBool hasMoreHistory = true.obs;
  final RxList<TypeItemData> typeItemList = <TypeItemData>[].obs;
  
  // 动态分类数据管理
  final RxMap<int, RxList<DanceMaterial>> justDanceMaterialMap = <int, RxList<DanceMaterial>>{}.obs;
  final RxMap<int, RxInt> pageMap = <int, RxInt>{}.obs;
  final RxMap<int, RxInt> totalPageMap = <int, RxInt>{}.obs;
  final RxMap<int, RxBool> isLoadingMap = <int, RxBool>{}.obs;
  final RxMap<int, RxBool> hasMoreMap = <int, RxBool>{}.obs;

  bool isSwitchingTab = false;
  double scrollOffset = 0.0;
  final int pageSize = 20;
  
  final RxBool isContentLoading = false.obs;
  final RxBool hasContentMore = false.obs;
  final Map<int, RxList<DanceMaterial>> contentDataMap = {};
  final Map<int, bool> contentLoadingStateMap = {};
  final Map<int, DateTime> contentLastRequestTimeMap = {};


  //============================================================
  // Getters
  //============================================================
  bool get _isUserLoggedIn => _prefs.getString('access_token') != null;
  User? get currentUser => userInfo.value;
  
  // Compatibility getters
  /// 用户信息的兼容属性
  Rx<User?> get user => userInfo;
  
  /// 检查用户是否已登录的公共属性
  bool get isUserLoggedIn => userInfo.value != null;

  //============================================================
  // 生命周期方法 (Lifecycle Methods)
  //============================================================

  @override
  void onInit() {
    super.onInit();
    _logger = Logger();
    // 异步初始化，但不阻塞Controller创建
    _initializeAsync();
  }

  Future<void> _initializeAsync() async {
    try {
      _prefs = await SharedPreferences.getInstance();

      if (!await _validateToken()) {
        return;
      }

      RawKeyboard.instance.addListener(_handleKeyEvent);

      await fetchUserInfo();
      await fetchJustDanceTypeList();
      preloadRecommendedData(); // 异步预加载，不等待

      // 初始化分类数据容器
      for (var i = 1; i < justDanceTypeList.value.length; i++) {
          final typeData = justDanceTypeList.value[i];
          final typeId = typeData.id;
          if (typeId != null) {
              justDanceMaterialMap[typeId] = <DanceMaterial>[].obs;
              pageMap[typeId] = 1.obs;
              totalPageMap[typeId] = 2.obs; // 初始假设至少有2页
              isLoadingMap[typeId] = true.obs;
              hasMoreMap[typeId] = false.obs;
          }
      }

      tabIndex.value = 0;
      selectedDanceTypeId.value = 0;
      await refreshDanceHistory();
      processTypeList();
    } catch (e) {
      _logger.e("HomeController异步初始化失败: $e");
    }
  }

  @override
  void onClose() {
    RawKeyboard.instance.removeListener(_handleKeyEvent);
    refreshController.dispose();
    super.onClose();
  }

  //============================================================
  // 核心业务逻辑 (Core Business Logic)
  //============================================================

  Future<bool> _validateToken() async {
    final accessToken = _prefs.getString('access_token');
    final refreshToken = _prefs.getString('refresh_token');

    if (accessToken == null || refreshToken == null) {
      await _handleTokenExpiration();
      return false;
    }
    return true;
  }
  
  Future<void> fetchUserInfo() async {
    try {
      final response = await _apiClient.post("/imiuser/getUser", data: {});
      
      if (response['code'] == 0) {
        if (response['data'] != null) {
          await _cacheService.setUserData(response['data']);
          final user = User.fromJson(response['data'] as Map<String, dynamic>);
          userInfo.value = user;

          if (_prefs != null && userInfo.value?.phone != null) {
            await _prefs.setString('MyPhone', userInfo.value!.phone!);
          }
          
          await _initializeVipCache();

          // 更新分析数据
          final isNewUser = DateTime.tryParse(userInfo.value?.createTime ?? '')?.isAfter(DateTime.now()) ?? false;
          await BaseAnalytics.updateUserInfo(
            userId: userInfo.value?.id.toString(),
            userNickname: userInfo.value?.nickname,
            userPhone: userInfo.value?.phone,
            endTime: userInfo.value?.vipExpireTime,
            isVip: userInfo.value?.isVip,
            isNewUser: isNewUser,
          );
        }
      } else {
        await _handleTokenExpiration();
      }
    } catch (e, s) {
      await _handleSmartError(e, s, "获取用户信息");
    }
  }

  Future<void> _initializeVipCache() async {
    try {
        if (userInfo.value == null) {
            _logger.w("用户信息为空，无法初始化VIP缓存");
            return;
        }

        final userId = userInfo.value!.id.toString();
        final vipExpireTimeStr = userInfo.value!.vipExpireTime;

        if (vipExpireTimeStr == null || vipExpireTimeStr.isEmpty) {
            await VipCacheService.clearVipInfo();
            _logger.d("用户无会员信息，已清除VIP缓存");
            return;
        }

        final vipExpireTime = DateTime.tryParse(vipExpireTimeStr);
        if (vipExpireTime == null || vipExpireTime.isBefore(DateTime.now())) {
            await VipCacheService.clearVipInfo();
            _logger.d("用户会员已过期，已清除VIP缓存");
            return;
        }

        await VipCacheService.inferVipLevelFromEndTime(vipExpireTimeStr, userId);
        _logger.d("VIP缓存初始化完成");

    } catch (e, s) {
        _logger.e("初始化VIP缓存失败: $e");
    }
  }

  Future<void> preloadRecommendedData() async {
    try {
      final tasks = <Future>[];
      tasks.add(fetchDanceThemeList());
      tasks.add(fetchUserDanceHistory(isLoadMore: false));
      await Future.wait(tasks);
    } catch (_) {
      // 预加载失败不中断主流程，可在此记录日志
    }
  }

  Future<void> fetchJustDanceTypeList() async {
    int retryCount = 0;
    while (retryCount < 3) {
      try {
        final response = await _apiClient.post(
          "/uaa/danceTypeRwxd/allDanceTypeList",
          data: {},
        );

        if (response['code'] == 0) {
          if (response['data'] is List) {
            final list = (response['data'] as List)
                .map((item) => DanceType.fromJson(item as Map<String, dynamic>))
                .toList();
            justDanceTypeList.value = list;
          }
          return; // 成功后退出循环
        } else {
          await _handleTokenExpiration();
          return; // Token问题，直接退出
        }
      } catch (e) {
        retryCount++;
        if (retryCount >= 3) {
          CommonTips.show("获取分类数据失败,请稍后重试");
          return;
        }
        await Future.delayed(const Duration(milliseconds: 500)); // 重试前等待
      }
    }
  }

  void processTypeList() {
    if (justDanceTypeList.value.isEmpty) {
      typeItemList.clear();
      return;
    }
    
    final List<TypeItemData> newTypeList = [];

    // 处理推荐页
    final recommendChildren = <TypeItemDataChild>[].obs;
    final recommendType = justDanceTypeList.value.first;
    recommendChildren.add(const TypeItemDataChild(id: null, name: '全部'));
    if (recommendType.children != null) {
      for (var child in recommendType.children!) {
        recommendChildren.add(TypeItemDataChild(id: child.id, name: child.typeName));
      }
    }
    newTypeList.add(TypeItemData(id: recommendType.id, name: recommendType.typeName, children: recommendChildren));

    // 处理其他分类
    for (int i = 1; i < justDanceTypeList.value.length; i++) {
        final typeData = justDanceTypeList.value[i];
        newTypeList.add(TypeItemData(id: typeData.id, name: typeData.typeName, children: <TypeItemDataChild>[].obs));
    }
    
    typeItemList.value = newTypeList;
  }
  
  Future<void> fetchDanceThemeList() async {
    isThemeLoading.value = true;
    try {
      final themes = await _danceThemeService.fetchDanceThemes();
      danceThemeList.value = themes;
    } catch (e, s) {
      danceThemeList.clear();
      await _handleSmartError(e, s, "获取舞蹈主题列表");
    } finally {
      isThemeLoading.value = false;
    }
  }
  
  Future<void> refreshDanceHistory() async {
    historyPage.value = 1;
    hasMoreHistory.value = true;
    isHistoryLoading.value = false;
    danceHistoryList.clear();
    await fetchUserDanceHistory(isLoadMore: false);
  }

  Future<void> loadMoreDanceHistory() async {
    if (!hasMoreHistory.value || isHistoryLoading.value) {
      return;
    }
    await fetchUserDanceHistory(isLoadMore: true);
  }

  Future<void> fetchUserDanceHistory({bool isLoadMore = false}) async {
    if (historyPage.value > 1 && !hasMoreHistory.value) {
        return;
    }
    
    if (isLoadMore) {
        isHistoryLoading.value = true;
    }

    try {
        final userData = await _cacheService.getUserData();
        final userId = userData?['id'];
        if (userId == null) {
            throw Exception("用户ID不存在");
        }
        
        final newHistory = await _danceHistoryService.fetchUserDanceHistory(
            historyPage.value,
            userId,
        );

        if (isLoadMore) {
            danceHistoryList.addAll(newHistory);
        } else {
            danceHistoryList.value = newHistory;
        }

        hasMoreHistory.value = newHistory.length >= 20;

        if (newHistory.isNotEmpty) {
            historyPage.value++;
        }
    } catch (e, s) {
        await _handleSmartError(e, s, "获取舞蹈历史");
    } finally {
        isHistoryLoading.value = false;
    }
  }

  Future<void> switchTab(int index) async {
    if (isSwitchingTab) return;

    isSwitchingTab = true;
    tabIndex.value = index;

    if (index > 0) { // 非推荐页
        final typeId = justDanceTypeList.value[index].id;
        if (typeId != null) {
            bool isEmpty = justDanceMaterialMap[typeId]?.value.isEmpty ?? true;
            if (isEmpty) {
                isContentLoading.value = true;
                
                final page = pageMap[typeId]?.value ?? 1;
                final totalPage = totalPageMap[typeId]?.value ?? 1;

                if (page <= totalPage) {
                   await fetchJustDanceCategoryData(typeId, isLoadMore: false);
                }
            }
        }
    }

    processTypeList(); // 切换后可能需要刷新类型列表
    isSwitchingTab = false;
    isContentLoading.value = false;
  }

  Future<void> fetchJustDanceCategoryData(int typeId, {bool isLoadMore = false}) async {
    if (isLoadingMap[typeId]?.value == true) return;
    
    if (!isLoadMore && hasMoreMap[typeId]?.value == false) {
        isLoadingMap[typeId]?.value = false; // 已无更多数据
        return;
    }

    isLoadingMap[typeId]?.value = true;
    
    final currentPage = isLoadMore ? (pageMap[typeId]?.value ?? 1) : 1;

    try {
        final headers = _getCommonHeaders();
        final materials = await _justDanceCategoryService.fetchJustDanceMaterialList(
            headers: headers,
            page: currentPage,
            typeId: typeId.toString(),
            subTypeIds: [],
        );

        if (materials.isEmpty) {
            isLoadingMap[typeId]?.value = false;
            return;
        }

        if (isLoadMore) {
            justDanceMaterialMap[typeId]?.addAll(materials.cast<DanceMaterial>());
            contentDataMap[typeId]?.addAll(materials.cast<DanceMaterial>());
        } else {
            justDanceMaterialMap[typeId]?.value = materials.cast<DanceMaterial>();
            contentDataMap[typeId] = materials.cast<DanceMaterial>().obs;
        }

        pageMap[typeId]?.value = currentPage + 1;
        hasMoreMap[typeId]?.value = materials.length >= 20;

    } catch (e) {
        _logger.e("加载数据失败: $e");
        if (!isLoadMore) {
            justDanceMaterialMap[typeId]?.clear();
            contentDataMap[typeId]?.clear();
        }
        isLoadingMap[typeId]?.value = false;
    } finally {
        isLoadingMap[typeId]?.value = false;
    }
  }


  //============================================================
  // 事件处理与辅助方法 (Event Handlers & Helpers)
  //============================================================

  void _handleKeyEvent(RawKeyEvent event) {
    if (event is RawKeyDownEvent) {
      // 在这里处理按键逻辑
      // 例如： if (RawKeyboard.instance.keysPressed.contains(LogicalKeyboardKey.arrowDown)) { ... }
    }
  }

  Future<void> _handleTokenExpiration() async {
    userInfo.value = null;
    danceHistoryList.clear();
    danceThemeList.clear();
    justDanceTypeList.clear();

    await VipCacheService.clearVipInfo();
    await _prefs.clear();
    await _cacheService.clearUserData();
    
    final loginRoute = _getLoginRoute();
    Get.offAllNamed(loginRoute);
  }

  Future<void> _performLogout() async {
    try {
      userInfo.value = null;
      danceHistoryList.clear();
      danceThemeList.clear();
      justDanceTypeList.clear();
      await VipCacheService.clearVipInfo();
      await _prefs.clear();
      await _cacheService.clearUserData();
    } catch(e) {
        _logger.e("登出操作失败: $e");
    }
  }

  Future<void> _handleSmartError(dynamic error, dynamic stack, String message) async {
    final errorType = _errorClassificationService.classifyError(error);
    await _errorClassificationService.handleError(
        errorType,
        () => _performLogout(),
        stack
    );
  }

  String _getLoginRoute() {
    final loginType = GlobalData.instance.loginType;
    if (loginType.isEmpty || loginType.contains("Phone")) {
      return "/login";
    }
    return "/login-page";
  }

  Map<String, String> _getCommonHeaders() {
    final accessToken = _prefs.getString('access_token') ?? '';
    final refreshToken = _prefs.getString('refresh_token') ?? '';
    return {
      'Content-Type': 'application/json',
      'access-token': accessToken,
      'appChannel': 'phone_jxhy',
      'device_sn': '2', // 注意：这里是硬编码的
      'refresh-token': refreshToken,
    };
  }
}

