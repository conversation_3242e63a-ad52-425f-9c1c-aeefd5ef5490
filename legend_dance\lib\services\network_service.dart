// lib: , url: package:keepdance/services/network_service.dart

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';

/// 网络服务类，用于管理网络连接状态和显示网络状态提示
class NetworkService extends GetxService {
  late final Logger _logger;
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  OverlayEntry? _overlayEntry;
  bool _isOverlayShown = false;

  @override
  void onInit() {
    super.onInit();
    _logger = Logger();
  }

  /// 初始化网络服务
  Future<NetworkService> init() async {
    try {
      await _initConnectivity();
      _setupConnectivityStream();
      return this;
    } catch (e) {
      _logger.e('NetworkService初始化失败: $e');
      rethrow;
    }
  }

  /// 初始化网络连接检查
  Future<void> _initConnectivity() async {
    try {
      final connectivity = Connectivity();
      final results = await connectivity.checkConnectivity();
      _updateConnectionStatus(results);
    } catch (e) {
      _logger.e('检查网络连接失败', error: e);
    }
  }

  /// 设置网络连接状态监听
  void _setupConnectivityStream() {
    final connectivity = Connectivity();
    _connectivitySubscription = connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      _updateConnectionStatus(results);
    });
  }

  /// 更新网络连接状态
  void _updateConnectionStatus(List<ConnectivityResult> results) {
    final isConnected =
        results.isNotEmpty && !results.contains(ConnectivityResult.none);

    if (isConnected) {
      _removeNoNetworkOverlay();
    } else {
      _showNoNetworkOverlay();
    }

    _logger.d('网络状态更新: ${results.map((r) => r.name).join(', ')}');
  }

  /// 显示无网络连接提示
  void _showNoNetworkOverlay() {
    if (_isOverlayShown) return;

    final context = Get.context;
    if (context == null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10,
        left: 20,
        right: 20,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.red.shade600,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.wifi_off, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '网络连接已断开',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _isOverlayShown = true;
  }

  /// 移除无网络连接提示
  void _removeNoNetworkOverlay() {
    if (!_isOverlayShown || _overlayEntry == null) return;

    try {
      _overlayEntry?.remove();
      _overlayEntry = null;
      _isOverlayShown = false;
    } catch (e) {
      _logger.w('移除网络提示失败: $e');
    }
  }

  /// 检查当前网络连接状态
  Future<bool> isConnected() async {
    try {
      final connectivity = Connectivity();
      final results = await connectivity.checkConnectivity();
      return results.isNotEmpty && !results.contains(ConnectivityResult.none);
    } catch (e) {
      _logger.e('检查网络状态失败: $e');
      return false;
    }
  }

  @override
  void onClose() {
    _removeNoNetworkOverlay();
    _connectivitySubscription.cancel();
    super.onClose();
  }
}
