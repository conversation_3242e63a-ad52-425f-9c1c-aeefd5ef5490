// lib: , url: package:keepdance/pages/creation/services/dance_import_service.dart

// 引入推测的或已知的包路径
import 'dart:io'; // 推测路径 (根据 _File, Directory, exists 等调用推断)
import 'dart:convert'; // 推测路径 (根据 Utf8Codec.decode 推断)
import 'package:archive/archive.dart'; // 推测路径 (根据 Archive, ZipDecoder, ArchiveFile 推断)
import 'package:get/get.dart'; // 推测路径 (根据 Get.find 推断)
import 'package:keepdance/common_widgets/common_tips.dart'; // 根据 CommonTips::show 调用推断
import 'package:keepdance/common_widgets/dialogs/duplicate_import_dialog.dart'; // 根据 DuplicateImportDialog::show 推断
import 'package:keepdance/models/work_item_data.dart';
import 'package:keepdance/models/local_dance_video.dart';
import 'package:keepdance/models/dance_category.dart' as dance_category;
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart'; // 根据 MyWorksController::相关调用推断
import 'package:keepdance/pages/creation/services/dance_package_compatibility_service.dart'; // 根据 DancePackageCompatibilityService::parseMetadata/parsePackageInfo 推断
import 'package:keepdance/pages/creation/services/dance_share_service.dart'; // 根据 _sanitizeFileName 调用推断
import 'package:keepdance/pages/creation/services/dance_encryption_service.dart'; // 根据 DanceEncryptionService 调用推断
import 'package:keepdance/pages/video_upload/services/local_storage_service.dart'; // 根据 LocalStorageService::saveDanceVideo 调用推断
import 'package:keepdance/services/global_import_state_manager.dart'; // 根据 GlobalImportStateManager::相关调用推断
import 'package:keepdance/utils/video/video_info_extractor.dart'; // 根据 VideoInfoExtractor::extractVideoInfo 调用推断
import 'package:keepdance/utils/video/video_resolution_util.dart'; // 根据 VideoResolutionUtil::getVideoResolution 推断
import 'package:keepdance/main.dart'; // 引入CustomApp
import 'package:logger/logger.dart'; // 根据 Logger::i/d/w/e 调用推断
import 'package:path/path.dart' as path; // 推测路径 (根据 path.join 推断)
import 'package:path_provider/path_provider.dart'; // 根据 getApplicationDocumentsDirectory 推断
import 'package:flutter/material.dart'; // 推测路径 (根据 MaterialColor 推断)

// class id: 1049795, size: 0x8
class UnusedClass {} // 对应 ::, 可能是编译器优化残留或空类

// class id: 1129, size: 0x8, field offset: 0x8
abstract class DanceImportService extends Object {
  static late final Logger _logger = CustomApp.logger;

  static Future<bool> _deleteExistingWork(String existingId) async {
    try {
      _logger.i("准备删除现有作品: $existingId");
      final MyWorksController worksController = Get.find<MyWorksController>();
      WorkItemData itemToDelete = worksController.worklistData.firstWhere(
        (element) => element.id == existingId,
        orElse: () => throw Exception("未找到要删除的作品"),
      );
      await worksController.deleteItem(itemToDelete);
      _logger.i("成功删除现有作品: ${itemToDelete.title}");
      return true;
    } catch (e) {
      _logger.e("删除现有作品时发生错误: $e");
      return false;
    }
  }

  static Future<bool> importDanceWorkFromFileService(String filePath) async {
    try {
      _logger.i("开始从FileImportService导入舞蹈作品: $filePath");
      return await _performImport(filePath, skipGlobalCheck: true);
    } catch (e) {
      _logger.e("从FileImportService导入舞蹈作品失败: $e");
      CommonTips.show(
        "导入失败: ${e.toString()}",
        backgroundColor: Colors.red, // 推断颜色
        position: TipsPosition.bottom, // 推断位置
        isGameStyle: true,
      );
      return false;
    }
  }

  static Future<bool> _performImport(
    String filePath, {
    bool skipGlobalCheck = false,
  }) async {
    if (!skipGlobalCheck) {
      if (!GlobalImportStateManager.tryStartImport(filePath, "舞蹈包导入")) {
        _logger.w("导入操作被阻止，可能已有相同或其他导入操作正在进行中");
        return false;
      }
    }

    try {
      File danceFile = File(filePath);
      if (!await danceFile.exists()) {
        CommonTips.show(
          "文件不存在",
          backgroundColor: Colors.red, // 推断颜色
          position: TipsPosition.bottom, // 推断位置
          isGameStyle: true,
        );
        if (!skipGlobalCheck) {
          GlobalImportStateManager.finishImport(filePath, false);
        }
        return false;
      }

      if (!path.basename(danceFile.path).endsWith(".kdance")) {
        CommonTips.show(
          "不支持的文件格式",
          backgroundColor: Colors.red, // 推断颜色
          position: TipsPosition.bottom, // 推断位置
          isGameStyle: true,
        );
        if (!skipGlobalCheck) {
          GlobalImportStateManager.finishImport(filePath, false);
        }
        return false;
      }

      CommonTips.show(
        "正在导入作品...",
        backgroundColor: Colors.blue, // 推断颜色
        position: TipsPosition.bottom, // 推断位置
        isGameStyle: true,
      );

      Archive? archive = await _extractArchive(danceFile);
      if (archive == null) {
        CommonTips.show(
          "文件格式错误，无法解压",
          backgroundColor: Colors.red, // 推断颜色
          position: TipsPosition.bottom, // 推断位置
          isGameStyle: true,
        );
        if (!skipGlobalCheck) {
          GlobalImportStateManager.finishImport(filePath, false);
        }
        return false;
      }

      Map<String, dynamic>? metadata = await _parseMetadata(archive);
      if (metadata == null) {
        CommonTips.show(
          "文件元数据损坏",
          backgroundColor: Colors.red, // 推断颜色
          position: TipsPosition.bottom, // 推断位置
          isGameStyle: true,
        );
        if (!skipGlobalCheck) {
          GlobalImportStateManager.finishImport(filePath, false);
        }
        return false;
      }

      final packageInfo = await _parsePackageInfo(archive);
      if (packageInfo == null) {
        CommonTips.show(
          "包信息验证失败",
          backgroundColor: Colors.red, // 推断颜色
          position: TipsPosition.bottom, // 推断位置
          isGameStyle: true,
        );
        if (!skipGlobalCheck) {
          GlobalImportStateManager.finishImport(filePath, false);
        }
        return false;
      }

      String newTitle = metadata['title'] as String;
      final duplicateInfo = await _checkDuplicateImport(metadata, newTitle);

      if (duplicateInfo != null) {
        _logger.i("检测到重复导入，显示用户选择对话框: $newTitle");

        final result = await DuplicateImportDialog.show(
          newItemName: newTitle,
          newDuration: duplicateInfo['newDuration'],
          existingItemName: duplicateInfo['existingTitle'],
          existingDuration: duplicateInfo['existingDuration'],
          caseName: duplicateInfo['reason'] ?? 'duplicate',
        );

        if (result == null ||
            result['action'] == DuplicateImportAction.cancel) {
          _logger.i("用户取消导入重复作品: $newTitle");
          CommonTips.show(
            "已取消导入",
            backgroundColor: Colors.grey, // 推断颜色
            position: TipsPosition.bottom, // 推断位置
            isGameStyle: true,
          );
          if (!skipGlobalCheck) {
            GlobalImportStateManager.finishImport(filePath, false);
          }
          return false;
        } else if (result['action'] == DuplicateImportAction.replace) {
          final String existingId = duplicateInfo['existingId'];
          _logger.i("用户选择替换现有作品: ${duplicateInfo['existingTitle']}");
          await _deleteExistingWork(existingId);
        } else if (result['action'] == DuplicateImportAction.rename) {
          final String renamedTitle = result['newName'];
          _logger.i("用户选择重命名导入: $newTitle -> $renamedTitle");
          metadata['title'] = renamedTitle;
          newTitle = renamedTitle;
        }
      }

      final workDirectory = await _createWorkDirectory(newTitle);
      final workItemData = await _extractAndSaveFiles(
        archive,
        metadata,
        workDirectory,
        newTitle,
      );

      if (workItemData == null) {
        CommonTips.show(
          "文件提取失败",
          backgroundColor: Colors.red, // 推断颜色
          position: TipsPosition.bottom, // 推断位置
          isGameStyle: true,
        );
        if (!skipGlobalCheck) {
          GlobalImportStateManager.finishImport(filePath, false);
        }
        return false;
      }

      final success = await _saveToLocalDatabase(workItemData);

      if (!success) {
        await _cleanupWorkDirectory(workDirectory);
        CommonTips.show(
          "保存到本地失败",
          backgroundColor: Colors.red, // 推断颜色
          position: TipsPosition.bottom, // 推断位置
          isGameStyle: true,
        );
        if (!skipGlobalCheck) {
          GlobalImportStateManager.finishImport(filePath, false);
        }
        return false;
      }

      await _refreshWorksList();

      // 清理静态字段
      // StoreStaticField(0x14f8, Null) -> dancedata.currentDanceVideo
      // StoreStaticField(0x14fc, Null) -> dancedata.currentMusicPath
      // StoreStaticField(0x1500, Null) -> dancedata.currentWorkItem
      // (This part is speculative and depends on the actual field names)

      CommonTips.show(
        "舞蹈作品导入成功！",
        backgroundColor: Colors.green, // 推断颜色
        position: TipsPosition.bottom, // 推断位置
        isGameStyle: true,
      );

      _logger.i("舞蹈作品导入成功: $newTitle");
      if (!skipGlobalCheck) {
        GlobalImportStateManager.finishImport(filePath, true);
      }
      return true;
    } catch (e) {
      _logger.e("执行导入逻辑失败: $e");
      CommonTips.show(
        "导入失败: ${e.toString()}",
        backgroundColor: Colors.red, // 推断颜色
        position: TipsPosition.bottom, // 推断位置
        isGameStyle: true,
      );
      if (!skipGlobalCheck) {
        GlobalImportStateManager.finishImport(filePath, false);
      }
      return false;
    }
  }

  static Future<void> _refreshWorksList() async {
    try {
      final MyWorksController worksController = Get.find<MyWorksController>();
      await worksController.refreshData();
      await worksController.refreshVipStatus();
      _logger.d("刷新作品列表和VIP状态成功");
    } catch (e) {
      _logger.w("刷新作品列表失败: $e");
    }
  }

  static Future<void> _cleanupWorkDirectory(Directory workDir) async {
    try {
      if (await workDir.exists()) {
        await workDir.delete(recursive: true);
        _logger.d("清理作品目录: ${workDir.path}");
      }
    } catch (e) {
      _logger.w("清理作品目录失败: $e");
    }
  }

  static Future<bool> _saveToLocalDatabase(WorkItemData workItem) async {
    try {
      final LocalStorageService storageService =
          Get.find<LocalStorageService>();
      int? durationInSeconds = workItem.extras?['duration'];
      _logger.d("准备保存到数据库: ${workItem.title}, 时长: ${durationInSeconds ?? 0}秒");

      String? packageId = workItem.extras?['packageId'];
      if (packageId != null) {
        _logger.i("从导入数据中读取到舞蹈包ID: $packageId");
      } else {
        _logger.w("导入的舞蹈包没有packageId信息");
      }

      String scoreModel = workItem.extras?['scoreModel'] ?? '3';
      _logger.i("从导入数据中读取评分模式: $scoreModel");

      bool showTips = workItem.extras?['showTips'] ?? true;
      _logger.i(
        "【DEBUG-导入】从workItem.extras中读取showTips: ${workItem.extras?['showTips']}, 类型: ${workItem.extras?['showTips'].runtimeType}",
      );
      _logger.i("【DEBUG-导入】解析后的showTips值: $showTips");

      LocalDanceVideo videoToSave = LocalDanceVideo(
        id: workItem.id ?? '',
        name: workItem.title ?? '',
        description: workItem.extras?['description'],
        videoPath: workItem.localVideoPath ?? workItem.videoUrl!,
        coverImagePath: workItem.localCoverPath,
        videoDurationSeconds: durationInSeconds ?? 0,
        category: DanceCategory(
          id: workItem.extras?['categoryId'] ?? '1',
          name: workItem.category?.name ?? '流行舞',
        ),
        level: DanceLevel.level1, // 使用默认值，避免复杂的枚举映射错误
        intensity: DanceIntensity.low, // 使用默认值，避免复杂的枚举映射错误
        isMirrorEnabled: workItem.extras?['isMirrorEnabled'] ?? false,
        showTips: showTips,
        createTime: DateTime.now(),
        fileSize: workItem.extras?['fileSize'] ?? 0,
        videoWidth: workItem.extras?['videoWidth'],
        videoHeight: workItem.extras?['videoHeight'],
        packageId: workItem.extras?['packageId'],
        scoreModel: workItem.extras?['scoreModel'] ?? '3',
      );

      if ((videoToSave.videoDurationSeconds ?? 0) <= 0) {
        _logger.w("警告：保存的作品时长为空或无效: ${videoToSave.duration}");
      } else {
        _logger.i("时长信息验证通过: ${videoToSave.duration}秒");
      }

      await storageService.saveDanceVideo(videoToSave);
      _logger.i("保存到本地数据库成功: ${workItem.title}, 时长: ${videoToSave.duration}秒");
      return true;
    } catch (e) {
      _logger.e("保存到本地数据库失败: $e");
      return false;
    }
  }

  static Future<int?> _getActualVideoDuration(String videoPath) async {
    try {
      _logger.d("开始获取视频实际时长: $videoPath");
      final videoInfo = await VideoInfoExtractor.extractVideoInfo(videoPath);
      final durationInSeconds = (videoInfo.duration!.inMicroseconds / 1000000)
          .round();
      _logger.i("通过VideoInfoExtractor获取时长: ${durationInSeconds}秒");
      return durationInSeconds;
    } catch (e) {
      _logger.e("获取视频实际时长失败: $e");
      return 0;
    }
  }

  static Future<WorkItemData?> _extractAndSaveFiles(
    Archive archive,
    Map<String, dynamic> metadata,
    Directory workDirectory,
    String title,
  ) async {
    try {
      _logger.i("【DEBUG-metadata结构】完整metadata: $metadata");
      _logger.i(
        "【DEBUG-metadata结构】category字段: ${metadata['category']}, 类型: ${metadata['category'].runtimeType}",
      );

      final videoFile = archive.files.firstWhere(
        (file) => file.name == 'video.mp4',
        orElse: () => null as ArchiveFile,
      );
      String? localVideoPath;
      if (videoFile?.content != null) {
        localVideoPath = path.join(workDirectory.path, 'video.mp4');
        File video = File(localVideoPath);
        await video.writeAsBytes(videoFile!.content as List<int>);
        _logger.d("保存视频文件: $localVideoPath");
      }

      final coverFile = archive.files.firstWhere(
        (file) => file.name == 'cover.jpg',
        orElse: () => null as ArchiveFile,
      );
      String? localCoverPath;
      if (coverFile?.content != null) {
        localCoverPath = path.join(workDirectory.path, 'cover.jpg');
        File cover = File(localCoverPath);
        await cover.writeAsBytes(coverFile!.content as List<int>);
        _logger.d("保存封面文件: $localCoverPath");
      }

      final bodyDataFile = archive.files.firstWhere(
        (file) => file.name == 'BodyData.txt',
        orElse: () => null as ArchiveFile,
      );
      String? localBodyDataPath;
      if (bodyDataFile?.content != null) {
        String bodyDataContent;
        String decodedContent = utf8.decode(bodyDataFile!.content as List<int>);

        if (DanceEncryptionService.isEncryptedTextData(decodedContent)) {
          _logger.d("检测到加密BodyData，开始解密...");
          String? decrypted = await DanceEncryptionService.decryptBodyData(
            decodedContent,
          );
          if (decrypted == null) {
            _logger.e("BodyData解密失败");
            throw Exception("BodyData解密失败，可能文件已损坏或版本不兼容");
          }
          _logger.i("BodyData解密成功！");
          bodyDataContent = decrypted;
        } else {
          _logger.d("检测到未加密BodyData，直接使用");
          bodyDataContent = decodedContent;
        }
        localBodyDataPath = path.join(workDirectory.path, 'BodyData.txt');
        await File(localBodyDataPath).writeAsString(bodyDataContent);
        _logger.d("保存姿势数据文件: $localBodyDataPath");
      }

      int? metadataDuration = metadata['duration'];
      int finalDuration;
      if (metadataDuration != null && metadataDuration > 0) {
        _logger.i("✅ metadata中的视频时长有效: $metadataDuration秒");
        finalDuration = metadataDuration;
      } else {
        _logger.w("⚠️ metadata中的视频时长无效: $metadataDuration秒，尝试从视频文件获取");
        if (localVideoPath != null) {
          finalDuration = await _getActualVideoDuration(localVideoPath) ?? 120;
          if (finalDuration <= 0) {
            _logger.w("⚠️ 无法获取视频时长，使用默认值: 120秒");
            finalDuration = 120;
          } else {
            _logger.i("✅ 从视频文件获取时长成功: ${finalDuration}秒");
          }
        } else {
          _logger.w("⚠️ 没有视频文件，使用默认时长: 120秒");
          finalDuration = 120;
        }
      }

      _logger.i(
        "导入时长信息: metadata时长=${metadata['duration']}秒, 最终使用=${finalDuration}秒",
      );

      int? videoWidth;
      int? videoHeight;
      if (localVideoPath != null) {
        final resolution = await VideoResolutionUtil.getVideoResolution(
          localVideoPath,
        );
        if (resolution != null) {
          videoWidth = resolution['width']?.toInt() ?? 0;
          videoHeight = resolution['height']?.toInt() ?? 0;
          _logger.i("获取视频分辨率: ${videoWidth}x${videoHeight}");
        } else {
          _logger.w("无法获取视频分辨率信息");
        }
      }

      String categoryId;
      String categoryName;

      if (metadata['category'] != null &&
          metadata['category'] is Map<String, dynamic>) {
        categoryId = metadata['category']['id']?.toString() ?? '1';
        categoryName = metadata['category']['name']?.toString() ?? '流行舞';
      } else if (metadata['category'] is String) {
        dance_category.DanceCategoryType? type =
            dance_category.DanceCategoryType.fromName(metadata['category']);
        categoryId = type?.id ?? '1';
        categoryName = type?.name ?? '流行舞';
      } else {
        categoryId = '1';
        categoryName = '流行舞';
      }
      _logger.i(
        "【DEBUG-category解析】最终categoryId: $categoryId, categoryName: $categoryName",
      );

      DateTime now = DateTime.now();
      String id =
          "${now.millisecondsSinceEpoch}_${(now.millisecondsSinceEpoch % 10000)}";
      String workPath = workDirectory.path;
      Map<String, dynamic> extras = {
        'fileSize': metadata['fileSize'] ?? 0,
        'duration': finalDuration,
        'categoryId': categoryId,
        'category': categoryName,
        'level': metadata['level'] ?? 'beginner',
        'intensity': metadata['intensity'] ?? 'moderate',
        'isMirrorEnabled': metadata['isMirrorEnabled'] ?? false,
        'showTips': metadata['showTips'] ?? true,
        'hasBodyData': localBodyDataPath != null,
        'scoreModel': metadata['scoreModel'] ?? '3',
        'importTime': now.toIso8601String(),
        'originalExportTime': metadata['exportTime'],
        'originalExportVersion': metadata['exportVersion'],
        'metadataDuration': metadata['duration'],
        'description': metadata['description'] ?? '',
      };

      if (metadata['packageId'] != null) {
        extras['packageId'] = metadata['packageId'];
      }
      if (videoWidth != null && videoHeight != null) {
        extras['videoWidth'] = videoWidth;
        extras['videoHeight'] = videoHeight;
        extras['isLandscape'] = videoWidth > videoHeight;
        extras['aspectRatio'] = videoWidth / videoHeight;
      }

      WorkItemData workItemData = WorkItemData(
        id: id,
        title: title,
        coverUrl: localCoverPath,
        videoUrl: localVideoPath,
        isPublic: false,
        publishTime: now,
        viewCount: 0,
        likeCount: 0,
        localVideoPath: localVideoPath,
        localCoverPath: localCoverPath,
        localBodyDataPath: localBodyDataPath,
        sharerId: metadata['sharerId'],
        sharerName: metadata['sharerName'],
        sharerAvatar: metadata['sharerAvatar'],
        extras: extras,
      );

      _logger.i(
        "创建WorkItemData成功: ${workItemData.title}, 时长: ${workItemData.extras?['duration']}秒",
      );
      return workItemData;
    } catch (e) {
      _logger.e("提取并保存文件失败: $e");
      return null;
    }
  }

  static Future<Directory> _createWorkDirectory(String title) async {
    final docsDir = await getApplicationDocumentsDirectory();
    final myWorksPath = path.join(docsDir.path, 'my_works');
    final myWorksDir = Directory(myWorksPath);
    if (!await myWorksDir.exists()) {
      await myWorksDir.create(recursive: true);
    }
    final sanitizedTitle = _sanitizeFileName(title);
    final workPath = path.join(myWorksPath, sanitizedTitle);
    final workDir = Directory(workPath);
    if (await workDir.exists()) {
      await workDir.delete(recursive: true);
    }
    await workDir.create(recursive: true);
    _logger.d("创建作品目录: $workPath");
    return workDir;
  }

  static Future<Map<String, dynamic>?> _checkDuplicateImport(
    Map<String, dynamic> metadata,
    String newTitle,
  ) async {
    try {
      final MyWorksController worksController = Get.find<MyWorksController>();
      _logger.d("🔄 强制刷新作品数据，确保重复检测使用最新状态");
      await worksController.refreshData();
      await Future.delayed(Duration(milliseconds: 100)); // 推断一个短暂的延迟

      final allWorks = worksController.worklistData.value;
      final int newDuration = metadata['duration'] ?? 0;
      final int newFileSize = metadata['fileSize'] ?? 0;
      final String? newPackageId = metadata['packageId'];

      _logger.d(
        "开始智能重复检测: title=$newTitle, duration=${newDuration}s, fileSize=${newFileSize}bytes, packageId=$newPackageId",
      );
      _logger.d("当前存在作品数量: ${allWorks.length}");

      for (var existingWork in allWorks) {
        String? existingPackageId = existingWork.extras?['packageId'];

        // 检查 packageId
        if (newPackageId != null && existingPackageId == newPackageId) {
          bool isSameName = existingWork.title == newTitle;
          String reason = isSameName ? "完全相同的作品（来自同一来源）" : "来自同一来源但名称已变更";
          String duplicateType = isSameName
              ? "exact_same_source"
              : "same_source_different_name";

          int existingDuration = existingWork.extras?['duration'] ?? 0;

          _logger.i(
            "检测到相同来源作品: 现有=\"${existingWork.title}\"(ID:${existingWork.id}), 导入=\"$newTitle\", 名称${isSameName ? '相同' : '不同'}",
          );

          return {
            'existingId': existingWork.id,
            'existingTitle': existingWork.title,
            'existingDuration': existingDuration > 0
                ? "${existingDuration}秒"
                : "未知",
            'newDuration': newDuration > 0 ? "${newDuration}秒" : "未知",
            'duplicateType': duplicateType,
            'reason': reason,
            'packageId': newPackageId,
          };
        }

        // 检查标题、时长、文件大小
        if (existingWork.title == newTitle) {
          bool hasDuration = newDuration > 0;
          bool isDurationSimilar = false;
          if (hasDuration) {
            int existingDuration = existingWork.extras?['duration'] ?? 0;
            if (existingDuration > 0) {
              isDurationSimilar = (newDuration - existingDuration).abs() <= 3;
            }
          }

          bool hasFileSize = newFileSize > 0;
          bool isFileSizeSimilar = false;
          if (hasFileSize) {
            int existingFileSize = existingWork.extras?['fileSize'] ?? 0;
            if (existingFileSize > 0) {
              isFileSizeSimilar =
                  (newFileSize - existingFileSize).abs() <=
                  (existingFileSize * 0.1).round();
            }
          }

          if (isDurationSimilar && isFileSizeSimilar) {
            _logger.i(
              "检测到完全重复作品: \"${existingWork.title}\"(ID:${existingWork.id}), 时长匹配=$isDurationSimilar, 文件大小匹配=$isFileSizeSimilar",
            );
            int existingDuration = existingWork.extras?['duration'] ?? 0;
            return {
              'existingId': existingWork.id,
              'existingTitle': existingWork.title,
              'existingDuration': existingDuration > 0
                  ? "${existingDuration}秒"
                  : "未知",
              'newDuration': newDuration > 0 ? "${newDuration}秒" : "未知",
              'duplicateType': 'exact_duplicate',
              'reason': '完全重复的作品（相同名称和内容）',
            };
          } else {
            _logger.i(
              "检测到相似作品: \"${existingWork.title}\"(ID:${existingWork.id}), 但时长或文件大小不同",
            );
            int existingDuration = existingWork.extras?['duration'] ?? 0;
            return {
              'existingId': existingWork.id,
              'existingTitle': existingWork.title,
              'existingDuration': existingDuration > 0
                  ? "${existingDuration}秒"
                  : "未知",
              'newDuration': newDuration > 0 ? "${newDuration}秒" : "未知",
              'duplicateType': 'similar_work',
              'reason': '相似作品（相同名称但内容可能不同）',
            };
          }
        }
      }

      _logger.d("未检测到重复作品，允许导入");
      return null;
    } on Exception catch (e) {
      _logger.e("无法获取MyWorksController，跳过重复检测: $e");
      return null;
    } catch (e) {
      _logger.e("智能重复检测失败: $e");
      return null;
    }
  }

  static Future<Map<String, dynamic>?> _parsePackageInfo(
    Archive archive,
  ) async {
    try {
      final packageInfoFile = archive.firstWhere(
        (file) => file.name == 'package_info.json',
        orElse: () => throw Exception('package_info.json not found'),
      );

      if (packageInfoFile.content == null) {
        throw Exception('package_info.json content is null');
      }

      final String content = utf8.decode(packageInfoFile.content as List<int>);
      final packageInfo = DancePackageCompatibilityService.parsePackageInfo(
        content,
      );
      _logger.i("兼容性服务解析包信息成功: ${packageInfo?['formatVersion']}");
      return packageInfo;
    } catch (e) {
      _logger.e("解析包信息失败: $e");
      _logger.w("使用默认package_info配置");
      return DancePackageCompatibilityService.parsePackageInfo("{}");
    }
  }

  static Future<Map<String, dynamic>?> _parseMetadata(Archive archive) async {
    try {
      final metadataFile = archive.firstWhere(
        (file) => file.name == 'metadata.json',
        orElse: () => throw Exception('metadata.json not found'),
      );

      if (metadataFile.content == null) {
        throw Exception('metadata.json content is null');
      }

      String content = utf8.decode(metadataFile.content as List<int>);
      if (DanceEncryptionService.isEncryptedTextData(content)) {
        _logger.d("检测到加密元数据，开始解密...");
        String? decrypted = await DanceEncryptionService.decryptMetadata(
          content,
        );
        if (decrypted == null) {
          _logger.e("元数据解密失败");
          throw Exception("元数据解密失败，可能文件已损坏或版本不兼容");
        }
        _logger.i("元数据解密成功！");
        content = decrypted;
      } else {
        _logger.d("检测到未加密元数据，使用兼容性服务解析");
      }

      final metadata = await DancePackageCompatibilityService.parseMetadata(
        content,
      );
      if (metadata == null) {
        throw Exception("兼容性服务无法解析元数据");
      }

      _logger.i("兼容性服务解析元数据成功: ${metadata['title']}");
      return metadata;
    } catch (e) {
      _logger.e("解析元数据失败: $e");
      return null;
    }
  }

  static Future<Archive?> _extractArchive(File danceFile) async {
    try {
      final bytes = await danceFile.readAsBytes();
      final fileSizeMB = (bytes.length / 1024 / 1024).toStringAsFixed(2);
      _logger.i("开始解压舞蹈包，文件大小: $fileSizeMB MB");

      final archive = ZipDecoder().decodeBytes(bytes);
      _logger.d("成功解压归档文件，包含 ${archive.numberOfFiles()} 个文件");
      return archive;
    } catch (e) {
      _logger.e("解压归档文件失败: $e");
      return null;
    }
  }

  static Future<bool> importDanceWorkWithoutQuotaCheck(String filePath) async {
    try {
      _logger.i("开始导入舞蹈作品文件（跳过配额检查）: $filePath");
      return await _performImport(filePath);
    } catch (e) {
      _logger.e("导入舞蹈作品失败: $e");
      CommonTips.show(
        "导入失败: ${e.toString()}",
        backgroundColor: Colors.red, // 推断颜色
        position: TipsPosition.bottom, // 推断位置
        isGameStyle: true,
      );
      return false;
    }
  }

  /// 清理文件名中的非法字符。
  static String _sanitizeFileName(String fileName) {
    // 替换所有非法字符为下划线
    String sanitized = fileName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');
    // 替换一个或多个空白字符为下划线
    sanitized = sanitized.replaceAll(RegExp(r'\s+'), '_');
    // 去除前后的空白字符和下划线
    sanitized = sanitized.trim().replaceAll(RegExp(r'^_+|_+$'), '');
    // 如果文件名为空或只包含非法字符，则使用默认名称
    return sanitized.isEmpty ? 'untitled' : sanitized;
  }
}
