import 'dart:async';
import 'package:flutter/services.dart';

class PoseLandmarkerPlugin {
  static const MethodChannel _channel = MethodChannel('pose_landmarker_plugin');

  /// 初始化姿势检测器
  static Future<void> initialize({int maxPoses = 1}) async {
    try {
      await _channel.invokeMethod('initialize', {'maxPoses': maxPoses});
    } on PlatformException catch (e) {
      throw PlatformException(
        code: e.code,
        message: e.message ?? 'Failed to initialize pose detector',
        details: e.details,
      );
    }
  }

  /// 开始姿势检测
  static Future<void> startDetection() async {
    try {
      await _channel.invokeMethod('startDetection');
    } on PlatformException catch (e) {
      throw PlatformException(
        code: e.code,
        message: e.message ?? 'Failed to start pose detection',
        details: e.details,
      );
    }
  }

  /// 停止姿势检测
  static Future<void> stopDetection() async {
    try {
      await _channel.invokeMethod('stopDetection');
    } on PlatformException catch (e) {
      throw PlatformException(
        code: e.code,
        message: e.message ?? 'Failed to stop pose detection',
        details: e.details,
      );
    }
  }

  /// 释放资源
  static Future<void> dispose() async {
    try {
      await _channel.invokeMethod('dispose');
    } on PlatformException catch (e) {
      throw PlatformException(
        code: e.code,
        message: e.message ?? 'Failed to dispose pose detector',
        details: e.details,
      );
    }
  }
}
