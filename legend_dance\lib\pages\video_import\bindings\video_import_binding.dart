import 'package:get/get.dart';
import 'package:logger/logger.dart';

import '../controllers/video_import_controller.dart';
import '../services/feature_intro_service.dart';
import '../../../main.dart';

class VideoImportBinding extends Bindings {
  @override
  void dependencies() {
    // VideoImportRecordService已在main.dart中注册，无需重复注册

    // 使用全局Logger并注册FeatureIntroService（永久存在）
    final logger = CustomApp.logger;
    Get.put<FeatureIntroService>(
      FeatureIntroService(logger: logger),
      permanent: true,
    );

    // 检查VideoImportController是否已注册，如果没有则注册
    if (!Get.isRegistered<VideoImportController>()) {
      Get.put<VideoImportController>(VideoImportController());
    }
  }
}
