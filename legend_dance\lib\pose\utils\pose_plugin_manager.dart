// lib: , url: package:keepdance/pose/utils/pose_plugin_manager.dart

import 'dart:async';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:keepdance/pose/utils/pose_plugin.dart';
import 'package:keepdance/pose/model/pose_joint.dart';

// class id: 1050114, size: 0x8
// 这是一个空的辅助类，在Dart代码中通常不需要，已省略。

// class id: 1693, size: 0x38, field offset: 0x18
class PosePluginManager extends GetxService {
  late final Logger _logger;
  late final PosePlugin _posePlugin;
  late final StreamController<List<List<PoseJoint>>> _poseResultController;

  bool _isInitialized = false;
  int _currentMaxPoses = 1;
  DateTime? _lastInitTime;
  final List<int> _detectionQualityHistory = [];

  // 静态字段，用于跟踪回调是否已设置
  static bool _isHandlerSet = false;

  PosePluginManager() {
    _logger = Logger(printer: PrettyPrinter(methodCount: 2));
    _posePlugin = PosePlugin();
    _poseResultController = StreamController<List<List<PoseJoint>>>.broadcast();
    // super.GetxService() 的调用是隐式的
  }

  Stream<List<List<PoseJoint>>> get poseStream => _poseResultController.stream;

  Future<void> initialize({
    bool forceReinitialize = false,
    int maxPoses = 1,
  }) async {
    try {
      if (_isInitialized && !forceReinitialize) {
        if (_currentMaxPoses == maxPoses) {
          _logger.d('PosePluginManager: 参数无变化，跳过初始化 (maxPoses: $maxPoses)');
          return;
        }

        // 热更新逻辑
        if (!_isDetectionQualityGood()) {
          // 如果检测质量不好，则进行完整的重新初始化
          if (!await _shouldReinitialize(
            forceReinitialize: forceReinitialize,
            maxPoses: maxPoses,
          )) {
            _logger.d('PosePluginManager: 智能判断不需要重新初始化');
            return;
          }
          await _performInitialization(forceReinitialize, maxPoses);
          return;
        }

        _logger.i(
          'PosePluginManager: 检测质量良好，尝试参数热更新 ($_currentMaxPoses -> $maxPoses)',
        );

        if (maxPoses < _currentMaxPoses) {
          // 降级场景 (例如，从双人降为单人)
          if (!_shouldAllowDowngrade()) {
            await _performInitialization(forceReinitialize, maxPoses);
            return;
          }
          _logger.i('PosePluginManager: 检测效果不佳，允许降级重新初始化');
        }

        if (maxPoses > _currentMaxPoses) {
          _logger.i(
            'PosePluginManager: 单人→双人，需要更新原生层maxPoses参数 ($_currentMaxPoses -> $maxPoses)',
          );
          try {
            await _posePlugin.initialize(
              forceReinitialize: false,
              maxPoses: maxPoses,
            );
            _currentMaxPoses = maxPoses;
            _logger.i('PosePluginManager: 成功更新原生层maxPoses参数到$maxPoses');
          } catch (e, s) {
            _logger.w(
              'PosePluginManager: 更新原生层参数失败，将进行完整重新初始化: $e',
              error: e,
              stackTrace: s,
            );
            // 更新失败，回退到完整初始化
            if (!await _shouldReinitialize(
              forceReinitialize: forceReinitialize,
              maxPoses: maxPoses,
            )) {
              _logger.d('PosePluginManager: 智能判断不需要重新初始化');
              return;
            }
            await _performInitialization(forceReinitialize, maxPoses);
          }
        } else {
          _logger.d('PosePluginManager: 保持当前检测器状态');
        }
        return;
      }

      // 首次初始化或强制重新初始化
      if (!await _shouldReinitialize(
        forceReinitialize: forceReinitialize,
        maxPoses: maxPoses,
      )) {
        _logger.d('PosePluginManager: 智能判断不需要重新初始化');
        return;
      }
      await _performInitialization(forceReinitialize, maxPoses);
    } on MissingPluginException {
      // 如果插件未实现（例如在当前平台不支持），则记录信息并正常运行
      _logger.d("姿势检测插件在当前平台暂不可用，应用将正常运行");
    } catch (e, s) {
      _logger.e('PosePluginManager: 初始化失败: $e', error: e, stackTrace: s);
      rethrow;
    }
  }

  Future<void> _performInitialization(
    bool forceReinitialize,
    int maxPoses,
  ) async {
    try {
      _logger.i(
        'PosePluginManager: 开始${forceReinitialize ? "强制" : "智能"}初始化 (maxPoses: $maxPoses)',
      );

      // 检查是否需要重新设置回调
      if (!_isHandlerSet || forceReinitialize) {
        // 重新初始化原生插件
        await _posePlugin.initialize(
          forceReinitialize: forceReinitialize,
          maxPoses: maxPoses,
        );
        // 设置回调
        _posePlugin.onPoseDetected = _handlePoseResult;
        _isHandlerSet = true;
        _logger.i(
          'PosePluginManager: 已设置${forceReinitialize ? "重新" : ""}全局回调处理器',
        );
      } else {
        // 仅更新参数，不重新设置回调
        await _posePlugin.initialize(
          forceReinitialize: false,
          maxPoses: maxPoses,
        );
        _logger.i('PosePluginManager: 已更新检测器参数');
      }

      // 更新状态
      _isInitialized = true;
      _currentMaxPoses = maxPoses;
      _lastInitTime = DateTime.now();
      _detectionQualityHistory.clear();

      _logger.i('PosePluginManager: 初始化完成 (maxPoses: $maxPoses)');
    } on MissingPluginException {
      // 如果插件未实现，记录信息但不抛出异常，让应用继续运行
      _logger.d("姿势检测插件在当前平台暂不可用，跳过初始化");
      // 即使插件初始化失败，也将状态设置为已初始化，避免重复尝试
      _isInitialized = true;
      _currentMaxPoses = maxPoses;
    } catch (e, s) {
      _logger.e('PosePluginManager: 执行初始化时失败: $e', error: e, stackTrace: s);
      rethrow;
    }
  }

  void _handlePoseResult(List<List<PoseJoint>> poses) {
    if (!_poseResultController.isClosed) {
      _updateDetectionQuality(poses.length);
      _poseResultController.add(poses);
    }
  }

  void _updateDetectionQuality(int detectedCount) {
    _detectionQualityHistory.add(detectedCount);
    // 仅保留最近10条记录
    if (_detectionQualityHistory.length > 10) {
      _detectionQualityHistory.removeAt(0);
    }
  }

  Future<bool> _shouldReinitialize({
    required bool forceReinitialize,
    required int maxPoses,
  }) async {
    if (forceReinitialize) {
      _logger.i('PosePluginManager: 强制重新初始化');
      return true;
    }
    if (!_isInitialized) {
      _logger.i('PosePluginManager: 首次初始化');
      return true;
    }

    if (_lastInitTime != null) {
      final difference = DateTime.now().difference(_lastInitTime!);
      if (difference.inMilliseconds < 1000) {
        _logger.w(
          'PosePluginManager: 防抖机制阻止重复初始化 (距离上次${difference.inMilliseconds}ms)',
        );
        return false;
      }
    }

    if (!_isDetectionQualityGood()) {
      _logger.w('PosePluginManager: 检测质量异常，允许重新初始化');
      return true;
    }

    // 检测质量良好时，尝试参数热更新
    _logger.i(
      'PosePluginManager: 检测质量良好，尝试参数热更新 ($_currentMaxPoses -> $maxPoses)',
    );

    // 如果是从双人模式降级到单人模式，需要检查是否允许降级
    if (maxPoses < _currentMaxPoses && _shouldAllowDowngrade()) {
      _logger.i('PosePluginManager: 检测效果不佳，允许降级重新初始化');
      return true;
    }

    // 如果是从单人模式升级到双人模式，需要更新原生层参数
    if (maxPoses > _currentMaxPoses) {
      _logger.i(
        'PosePluginManager: 单人→双人，需要更新原生层maxPoses参数 ($_currentMaxPoses -> $maxPoses)',
      );
      return true;
    }

    return false;
  }

  bool _shouldAllowDowngrade() {
    // 如果历史记录不足，则不允许降级
    if (_detectionQualityHistory.length < 5) {
      return false;
    }
    // 计算最近5次检测的平均人数
    final recentHistory = _detectionQualityHistory.take(5).toList();
    final sum = recentHistory.fold<int>(0, (a, b) => a + b);
    final average = sum / 5.0;
    // 如果平均检测人数 < 1.5，则认为效果不佳，允许降级
    return average < 1.5;
  }

  bool _isDetectionQualityGood() {
    // 如果没有历史记录，则认为质量良好
    if (_detectionQualityHistory.isEmpty) {
      return true;
    }

    // 检查最近5次检测中，为0的次数是否过多
    final zeroCount = _detectionQualityHistory
        .take(5)
        .where((count) => count == 0)
        .length;
    if (zeroCount >= 3) {
      _logger.w('PosePluginManager: 检测质量异常，最近5次检测中有$zeroCount次为0');
      return false;
    }

    // 如果是双人模式，且有足够的历史记录，则检查平均检测人数
    if (_currentMaxPoses >= 2 && _detectionQualityHistory.length >= 3) {
      final recentHistory = _detectionQualityHistory.take(3).toList();
      final sum = recentHistory.fold<int>(0, (a, b) => a + b);
      final average = sum / 3.0;
      if (average < 1.2) {
        _logger.w(
          'PosePluginManager: 双人检测质量不佳，平均检测人数: ${average.toStringAsFixed(1)}',
        );
        return false;
      }
    }

    return true;
  }

  void resetForArchitectureSwitch() {
    _logger.i('PosePluginManager: 🔄 架构切换状态重置');
    _isInitialized = false;
    _currentMaxPoses = 1;
    _isHandlerSet = false;
    _detectionQualityHistory.clear();
    _lastInitTime = null;
    _posePlugin.onPoseDetected = null;
    _logger.i('PosePluginManager: ✅ 架构切换状态重置完成');
  }

  @override
  void onClose() {
    _logger.i('PosePluginManager: 正在关闭...');
    _poseResultController.close();
    super.onClose();
  }
}
