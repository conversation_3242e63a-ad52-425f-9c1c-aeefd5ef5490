import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:keepdance/services/cache_service.dart';
import 'package:keepdance/pages/video_detail/states/playback_speed_state.dart';
import 'package:logger/logger.dart';

// 这是一个空的辅助类，对应于 class id: 1049995
// class :: {}

class PlaybackSpeedService extends GetxService {
  // 这些字段是根据汇编代码中的字段访问推断出来的
  // field_17 -> _logger (推断)
  // field_1f -> _cacheService (推断)
  // field_1b -> _state (推断)
  final Logger _logger = Logger();
  late final CacheService _cacheService;
  late final PlaybackSpeedState _state;

  @override
  void onInit() async {
    super.onInit();
    try {
      _logger.d("PlaybackSpeedService 初始化");

      // _cacheService = Get.find<CacheService>();
      _cacheService = Get.find<CacheService>();

      // if (!Get.isRegistered<PlaybackSpeedState>()) {
      //   _state = Get.put(PlaybackSpeedState());
      // } else {
      //   _state = Get.find<PlaybackSpeedState>();
      // }
      if (!Get.isRegistered<PlaybackSpeedState>()) {
        _state = Get.put(PlaybackSpeedState(), permanent: true);
      } else {
        _state = Get.find<PlaybackSpeedState>();
      }

      _loadVipStatusInBackground();
    } catch (e, s) {
      _logger.e("PlaybackSpeedService 初始化失败: $e");
      // 安全措施：如果state初始化失败，则通过Get注册一个
      if (Get.context != null && !Get.isRegistered<PlaybackSpeedState>()) {
        _state = Get.put(PlaybackSpeedState(), permanent: true);
      }
    }
  }

  @override
  void onClose() {
    _logger.d("PlaybackSpeedService 销毁");
    super.onClose();
  }

  /// 在后台加载并同步VIP状态
  Future<void> _loadVipStatusInBackground() async {
    try {
      final bool isVip = await _loadVipStatus();
      _state.updateVipStatus(isVip);
      _logger.d("✅ VIP状态同步完成: $isVip");
    } catch (e, s) {
      _logger.e("❌ VIP状态同步失败: $e");
    }
  }

  /// 获取用户VIP状态
  Future<bool> _loadVipStatus() async {
    try {
      // 汇编中检查了 _cacheService 是否为 null
      // if (_cacheService == null) {
      //   _logger.w("CacheService 未初始化，使用默认VIP状态");
      //   return false;
      // }

      final userData = await _cacheService.getUserData();
      if (userData == null) {
        return false;
      }

      // 检查 'endTime' 键是否存在且不为 null
      if (userData.containsKey('endTime') && userData['endTime'] != null) {
        final dynamic endTimeValue = userData['endTime'];
        if (endTimeValue is String) {
          final DateTime? endTime = DateTime.tryParse(endTimeValue);
          if (endTime != null) {
            return endTime.isAfter(DateTime.now());
          }
        }
      }

      return false;
    } catch (e, s) {
      _logger.e("获取VIP状态失败: $e");
      return false;
    }
  }

  /// 设置播放速度
  Future<bool> setPlaybackSpeed(double speed) async {
    try {
      // 汇编中检查了 _state 是否为 null
      // if (_state == null) {
      //   _logger.e("PlaybackSpeedState 未初始化");
      //   return false;
      // }

      final availableSpeeds = _state.getAllAvailableSpeeds();
      if (!availableSpeeds.contains(speed)) {
        _logger.w("倍速不可用: ${speed}x");
        return false;
      }

      if (_state.isVipSpeedRequired(speed)) {
        if (!_state.isVip.value) {
          _logger.w("倍速需要VIP权限: ${speed}x");
          return false;
        }
      }

      _state.setCurrentSpeed(speed);
      HapticFeedback.lightImpact(); // 触发轻微震动反馈
      _logger.d("倍速设置成功: ${speed}x");
      return true;
    } catch (e, s) {
      _logger.e("设置倍速失败: $e");
      return false;
    }
  }
}
