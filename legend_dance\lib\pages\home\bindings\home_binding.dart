import 'package:get/get.dart';

import '../controllers/home_controller.dart';
import '../controllers/recommended_panel_controller.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    // 懒加载注册HomeController
    Get.lazyPut<HomeController>(() => HomeController());

    // 懒加载注册RecommendedPanelController
    Get.lazyPut<RecommendedPanelController>(() => RecommendedPanelController());

    // MainController已在main.dart中注册，无需重复注册
  }
}
