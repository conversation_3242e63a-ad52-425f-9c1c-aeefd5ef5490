import 'dart:async';

import 'package:get/get.dart';
import 'package:logger/logger.dart';

// 假设的依赖项路径，请根据您的项目结构进行修改
import './my_works_service.dart';
import 'package:keepdance/services/local_video_score_service.dart';
import './image_optimization_service.dart';
import 'package:keepdance/models/work_item_data.dart';

class OfflineFirstLoadResult extends Object {
  final List<WorkItemData> works;
  final String message;
  final bool usedCache;
  final Object? error;

  OfflineFirstLoadResult({
    required this.works,
    required this.message,
    required this.usedCache,
    this.error,
  });
}

/// 创作页面预加载服务
///
/// 负责在应用启动或特定时机提前加载和缓存创作页面所需的数据，
/// 以提升页面的加载速度和用户体验。
class CreationPreloadService extends GetxService {
  // 静态日志记录器
  static final Logger _logger = Logger();

  // 依赖的服务
  MyWorksService? _myWorksService;
  LocalVideoScoreService? _scoreService;
  ImageOptimizationService? _imageService;

  // 内部状态和缓存
  final List<WorkItemData> _worksCache = <WorkItemData>[];
  final Map<String, List<WorkItemData>> _categoryCache =
      <String, List<WorkItemData>>{};

  // GetX 响应式状态
  /// 是否已完成预加载
  final RxBool isPreloaded = false.obs;

  /// 是否正在刷新数据
  final RxBool isRefreshing = false.obs;

  /// 预加载状态文本
  final RxString preloadStatus = '准备中'.obs;

  /// 预加载进度
  final RxDouble preloadProgress = 0.0.obs;

  // 预加载超时计时器
  Timer? _preloadTimer;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeDependencies();
    _logger.i("创作页面预加载服务初始化完成");
  }

  @override
  void onClose() {
    _preloadTimer?.cancel();
    _clearCache();
    // 调整：调用 super.onClose() 是 GetX 的最佳实践，以确保所有响应式变量的控制器都被正确关闭。
    // 原始汇编中未明确显示此调用，但为了代码的健壮性和规范性，此处予以添加。
    super.onClose();
  }

  /// 初始化服务依赖
  Future<void> _initializeDependencies() async {
    try {
      // 使用安全的方式查找服务，如果服务不存在则跳过
      try {
        _myWorksService = Get.find<MyWorksService>();
      } catch (e) {
        _logger.w("MyWorksService 未找到，将在需要时重新尝试: $e");
      }

      try {
        _scoreService = Get.find<LocalVideoScoreService>();
        // 异步初始化评分数据库，不阻塞后续流程
        _scoreService
            ?.ensureDatabaseInitialized()
            .then((_) {
              _logger.d("评分服务数据库异步初始化完成");
            })
            .catchError((error, stackTrace) {
              _logger.w("评分服务数据库异步初始化失败: $error");
            });
      } catch (e) {
        _logger.w("LocalVideoScoreService 未找到，将在需要时重新尝试: $e");
      }

      try {
        _imageService = Get.find<ImageOptimizationService>();
      } catch (e) {
        _logger.w("ImageOptimizationService 未找到，将在需要时重新尝试: $e");
      }

      _logger.d("预加载服务依赖初始化完成");
    } catch (e, s) {
      _logger.e("预加载服务依赖初始化失败: $e");
      // 不重新抛出异常，让服务继续初始化
    }
  }

  /// 开始预热加载。
  /// 如果已加载，则跳过。
  Future<void> startPrewarmLoading() async {
    if (isPreloaded.value) {
      _logger.d("预加载已完成，跳过重复执行");
      return;
    }

    try {
      _logger.i("开始预热加载创作页面数据");
      preloadStatus.value = "正在预热加载...";
      preloadProgress.value = 0.0;

      // 设置一个超时计时器
      _preloadTimer = Timer(const Duration(seconds: 15), () {
        if (!isPreloaded.value) {
          _logger.w("预加载超时，使用已加载的数据");
          _completePreload();
        }
      });

      await _loadCompleteData();
      _completePreload();
    } catch (e) {
      _logger.e("预热加载失败: $e");
      preloadStatus.value = "预加载失败";
      // 即使失败，也调用完成方法来更新状态
      _completePreload();
    }
  }

  /// 使用离线优先策略加载作品数据。
  /// 如果缓存可用，则立即返回缓存；否则，从网络加载。
  Future<OfflineFirstLoadResult> loadWorksWithOfflineFirst() async {
    _logger.d("开始离线优先加载策略");

    if (isPreloaded.value) {
      if (_worksCache.isNotEmpty) {
        _logger.i("使用预加载缓存数据: ${_worksCache.length} 个作品");
        return OfflineFirstLoadResult(
          works: List.from(_worksCache), // 返回缓存副本
          message: "已加载缓存数据",
          usedCache: true,
        );
      } else {
        _logger.d("预加载完成但无作品数据");
        return OfflineFirstLoadResult(
          works: [],
          message: "无数据",
          usedCache: false,
        );
      }
    }

    _logger.d("预加载未完成，直接加载完整数据");
    try {
      if (_myWorksService == null) {
        _logger.w("MyWorksService 未初始化，无法加载数据");
        return OfflineFirstLoadResult(
          works: [],
          message: "服务未初始化",
          usedCache: false,
        );
      }
      final List<WorkItemData> works = await _myWorksService!.loadLocalWorks();
      _updateCache(works);
      isPreloaded.value = true;
      _logger.i("完整数据加载完成: ${works.length} 个作品");
      return OfflineFirstLoadResult(
        works: works,
        message: "数据加载完成",
        usedCache: false,
      );
    } catch (e) {
      _logger.e("完整加载失败: $e");
      return OfflineFirstLoadResult(
        works: [],
        message: "加载失败",
        usedCache: false,
        error: e,
      );
    }
  }

  /// 使用离线优先策略刷新作品数据。
  /// 此方法会强制从网络加载最新数据，同时防止并发刷新。
  Future<OfflineFirstLoadResult> refreshWorksWithOfflineFirst() async {
    _logger.d("开始快速刷新策略");

    if (isRefreshing.value) {
      _logger.d("刷新正在进行中，返回当前缓存数据");
      return OfflineFirstLoadResult(
        works: List.from(_worksCache),
        message: "正在刷新中...",
        usedCache: true,
      );
    }

    isRefreshing.value = true;
    try {
      if (_myWorksService == null) {
        _logger.w("MyWorksService 未初始化，无法刷新数据");
        return OfflineFirstLoadResult(
          works: List.from(_worksCache),
          message: "服务未初始化，返回缓存数据",
          usedCache: true,
        );
      }
      final List<WorkItemData> works = await _myWorksService!.loadLocalWorks();
      _updateCache(works);
      _logger.i("快速刷新完成: ${works.length} 个作品");
      return OfflineFirstLoadResult(
        works: works,
        message: "刷新完成",
        usedCache: false,
      );
    } catch (e) {
      _logger.e("快速刷新失败: $e");
      if (_worksCache.isNotEmpty) {
        // 刷新失败但有旧缓存，返回旧缓存
        return OfflineFirstLoadResult(
          works: List.from(_worksCache),
          message: "刷新失败，显示缓存数据",
          usedCache: true,
          error: e,
        );
      } else {
        // 刷新失败且无缓存
        return OfflineFirstLoadResult(
          works: [],
          message: "刷新失败",
          usedCache: false,
          error: e,
        );
      }
    } finally {
      isRefreshing.value = false;
    }
  }

  /// 完成预加载流程，更新状态。
  void _completePreload() {
    _preloadTimer?.cancel();
    _preloadTimer = null;
    isPreloaded.value = true;
    preloadStatus.value = "预加载完成";
    preloadProgress.value = 1.0;
    _logger.i("预加载完成，缓存 ${_worksCache.length} 个作品");
  }

  /// 加载完整数据并更新缓存的内部实现。
  Future<void> _loadCompleteData() async {
    preloadStatus.value = "扫描本地作品...";
    preloadProgress.value = 0.2;

    if (_myWorksService == null) {
      _logger.w("MyWorksService 未初始化，跳过预加载");
      return;
    }

    final List<WorkItemData> localWorks = await _myWorksService!
        .loadLocalWorks();

    preloadStatus.value = "处理完整信息...";
    preloadProgress.value = 0.6;

    _updateCache(localWorks);

    preloadStatus.value = "准备完成...";
    preloadProgress.value = 0.8;

    _logger.i("完整数据加载完成: ${localWorks.length} 个作品");
  }

  /// 使用新数据更新内部缓存。
  void _updateCache(Iterable<WorkItemData> newWorks) {
    final worksList = newWorks.toList();

    _worksCache.clear();
    _worksCache.addAll(worksList);

    _categoryCache.clear();

    for (final item in worksList) {
      // 假设 WorkItemData 有 categoryId 属性
      final categoryId = item.categoryId;
      if (categoryId != null) {
        final key = 'category_$categoryId';
        _categoryCache.putIfAbsent(key, () => <WorkItemData>[]).add(item);
      }
    }

    _logger.d("缓存更新完成: ${_worksCache.length} 个作品，${_categoryCache.length} 个分类");
  }

  /// 清理所有缓存数据。
  void _clearCache() {
    _worksCache.clear();
    _categoryCache.clear();
    _logger.d("缓存已清理");
  }
}
