import 'dart:io';

import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:keepdance/models/work_item_data.dart';
import 'package:keepdance/models/local_dance_video.dart' as LocalVideo;
import 'package:keepdance/models/dance_attributes.dart' as Attributes;
import 'package:keepdance/models/dance_category.dart';
import 'package:keepdance/utils/video/video_info_extractor.dart';
import 'package:keepdance/pages/video_upload/services/local_storage_service.dart';
import 'package:keepdance/services/local_video_score_service.dart';
import 'package:keepdance/pages/video_import/services/video_import_record_service.dart';

class WorksStatisticsService extends GetxService {
  void _notifyDataChanged(String reason) {}
}

class AnnotationCleanupService extends GetxService {
  Future<void> cleanupAfterVideoDelete(
    String videoPath,
    String danceName,
    String? videoId,
  ) async {}
}

class VideoDimensionInferrer {
  static Future<VideoDimensionResult> inferVideoDimensions(
    String name,
    String? path,
  ) async => VideoDimensionResult();
  static VideoDimensionResult getPortraitDefault() => VideoDimensionResult();
}

// VideoInfoExtractor class removed - using import from utils/video/video_info_extractor.dart
class VideoResolutionUtil {
  static String getResolutionDescription(int width, int height) => '';
}

// WorkItemData class removed - using import from models/work_item_data.dart
// LocalDanceVideo class removed - using import from models/local_dance_video.dart
class LocalVideoScore {
  int? score;
}

// DanceCategory and DanceCategoryType are imported from dance_category.dart
enum DanceLevel { easy, normal, hard, expert, master }

enum DanceIntensity { low, medium, high, veryHigh }

class VideoDimensionResult {
  int? width;
  int? height;
  bool? isLandscape;
  double? aspectRatio;
  String? resolutionDescription;
  bool? is4K;
}

class VideoInfo {
  int? durationUs;
}
// --- 占位符定义结束 ---

class MyWorksService extends GetxService {
  final _log = Logger();
  late final LocalStorageService _localStorageService;
  late final VideoImportRecordService _videoImportRecordService;

  @override
  void onInit() {
    super.onInit();
    _initializeDependencies();
  }

  void _initializeDependencies() {
    // 依赖注入
    _localStorageService = Get.find<LocalStorageService>();
    if (!Get.isRegistered<VideoImportRecordService>()) {
      Get.put(VideoImportRecordService(), permanent: true);
    }
    _videoImportRecordService = Get.find<VideoImportRecordService>();
    if (!Get.isRegistered<AnnotationCleanupService>()) {
      Get.put(AnnotationCleanupService(), permanent: true);
    }
  }

  /// 加载本地作品列表
  Future<List<WorkItemData>> loadLocalWorks() async {
    try {
      final localVideos = await _localStorageService.getAllDanceVideos();
      return await _processLocalVideos(localVideos);
    } catch (e, stackTrace) {
      _log.e("加载本地作品时出错: $e");
      return [];
    }
  }

  /// 处理从数据库加载的视频列表，并转换为可供UI使用的WorkItemData列表
  Future<List<WorkItemData>> _processLocalVideos(
    List<LocalVideo.LocalDanceVideo> localVideos,
  ) async {
    try {
      final List<WorkItemData> workItems = [];
      final localVideoScoreService = Get.find<LocalVideoScoreService>();
      await localVideoScoreService.ensureDatabaseInitialized();

      for (var video in localVideos) {
        final Map<String, dynamic> extraData = {};

        // 1. 处理视频时长
        if (video.duration == null || video.duration! <= 0) {
          _log.w("本地作品 ${video.name} 数据库中没有时长信息，尝试从视频文件获取");
          if (video.videoPath != null && video.videoPath!.isNotEmpty) {
            final file = File(video.videoPath!);
            if (file.existsSync()) {
              try {
                final videoInfo = await VideoInfoExtractor.extractVideoInfo(
                  video.videoPath!,
                );
                final durationInSeconds = (videoInfo.durationUs! / 1000000)
                    .round();
                _log.i("✅ 从视频文件获取时长成功: ${video.name} = $durationInSeconds秒");

                final success = await _localStorageService
                    .updateDanceVideoDuration(video.id!, durationInSeconds);
                if (success) {
                  _log.d("✅ 已更新数据库中的时长信息: ${video.name}");
                } else {
                  _log.w("⚠️ 更新数据库时长信息失败: ${video.name}");
                }
                extraData['duration'] = durationInSeconds;
              } catch (e, stackTrace) {
                _log.e("❌ 获取视频时长失败: ${video.name}, 错误: $e，使用默认值120秒");
                extraData['duration'] = 120;
              }
            } else {
              _log.w("⚠️ 视频文件不存在: ${video.videoPath}，使用默认值120秒");
              extraData['duration'] = 120;
            }
          } else {
            // 路径为空，也使用默认值
            extraData['duration'] = 120;
          }
        } else {
          extraData['duration'] = video.duration;
        }

        // 2. 获取最高分
        try {
          final bestScore = await localVideoScoreService.getBestLocalVideoScore(
            video.id!,
          );
          if (bestScore != null) {
            extraData['maxScore'] = bestScore.max_score;
          }
        } catch (e, stackTrace) {
          _log.w("获取作品\"${video.name}\"评级数据失败: $e");
        }

        // 3. 处理视频尺寸信息
        if (video.videoWidth != null && video.videoHeight != null) {
          final width = video.videoWidth!;
          final height = video.videoHeight!;
          extraData['videoWidth'] = width;
          extraData['videoHeight'] = height;
          if (width > 0 && height > 0) {
            final isLandscape = width > height;
            extraData['isLandscape'] = isLandscape;
            extraData['aspectRatio'] = width / height;
          } else {
            extraData['isLandscape'] = false;
            extraData['aspectRatio'] = 1.0;
          }
          extraData['resolutionDescription'] =
              VideoResolutionUtil.getResolutionDescription(width, height);
          extraData['is4K'] = width >= 3840 && height >= 2160;
        } else {
          try {
            final dimensions =
                await VideoDimensionInferrer.inferVideoDimensions(
                  video.name!,
                  video.videoPath,
                );
            extraData['videoWidth'] = dimensions.width;
            extraData['videoHeight'] = dimensions.height;
            extraData['isLandscape'] = dimensions.isLandscape;
            extraData['aspectRatio'] = dimensions.aspectRatio;
            extraData['resolutionDescription'] =
                dimensions.resolutionDescription;
            extraData['is4K'] = dimensions.is4K;
          } catch (e) {
            // 如果推断失败，使用默认值
            final dimensions = VideoDimensionInferrer.getPortraitDefault();
            extraData['videoWidth'] = dimensions.width;
            extraData['videoHeight'] = dimensions.height;
            extraData['isLandscape'] = dimensions.isLandscape;
            extraData['aspectRatio'] = dimensions.aspectRatio;
            extraData['resolutionDescription'] =
                dimensions.resolutionDescription;
            extraData['is4K'] = dimensions.is4K;
          }
        }

        // 4. 转换难度和强度等级为数值
        if (video.level != null) {
          switch (video.level!.index) {
            case 0:
              extraData['difficultyLevel'] = 2;
              break;
            case 1:
              extraData['difficultyLevel'] = 4;
              break;
            case 2:
              extraData['difficultyLevel'] = 6;
              break;
            case 3:
              extraData['difficultyLevel'] = 8;
              break;
            default:
              extraData['difficultyLevel'] = 10;
              break;
          }
        }
        if (video.intensity != null) {
          switch (video.intensity!.index) {
            case 0:
              extraData['intensityLevel'] = 2;
              break;
            case 1:
              extraData['intensityLevel'] = 4;
              break;
            case 2:
              extraData['intensityLevel'] = 6;
              break;
            default:
              extraData['intensityLevel'] = 8;
              break;
          }
        }

        // 5. 填充其他数据
        extraData['fileSize'] = video.fileSize;
        extraData['category'] = video.category?.name;
        extraData['categoryId'] = video.category?.id;
        extraData['level'] = video.level?.name;
        extraData['intensity'] = video.intensity?.name;
        extraData['hasBodyData'] =
            video.bodyDataPath != null && video.bodyDataPath!.isNotEmpty;
        extraData['bodyDataPath'] = video.bodyDataPath;
        extraData['isMirrorEnabled'] = video.isMirrorEnabled;
        extraData['showTips'] = video.showTips;
        extraData['hasCover'] =
            video.coverImagePath != null && video.coverImagePath!.isNotEmpty;
        extraData['createTime'] = video.createTime?.toIso8601String();
        if (video.originalImportPath != null &&
            video.originalImportPath!.isNotEmpty) {
          extraData['originalImportPath'] = video.originalImportPath;
        }
        if (video.description != null && video.description!.isNotEmpty) {
          extraData['description'] = video.description;
        }
        if (video.packageId != null && video.packageId!.isNotEmpty) {
          extraData['packageId'] = video.packageId;
        }
        extraData['scoreModel'] = video.scoreModel;

        // Add additional data to extras map
        extraData['isLocal'] = true;
        extraData['description'] = video.description ?? '';
        extraData['originalImportPath'] = video.originalImportPath;
        extraData['isMirrorEnabled'] = video.isMirrorEnabled;
        extraData['showTips'] = video.showTips;
        extraData['fileSize'] = video.fileSize;
        extraData['packageId'] = video.packageId;

        final workItem = WorkItemData(
          id: video.id,
          title: video.name,
          localVideoPath: video.videoPath,
          localCoverPath: video.coverImagePath,
          localBodyDataPath: video.bodyDataPath,
          publishTime: video.createTime,
          extras: extraData,
        );
        workItems.add(workItem);
      }
      return workItems;
    } catch (e, stackTrace) {
      _log.e("加载本地作品时出错: $e");
      return [];
    }
  }

  /// 更新作品信息
  Future<bool> updateWorkItem(
    WorkItemData workItem,
    Map<String, dynamic> updateData,
  ) async {
    try {
      final LocalVideo.LocalDanceVideo? video = await _localStorageService
          .getDanceVideoById(workItem.id!);
      if (video == null) {
        _log.w("尝试更新一个不存在的作品: ${workItem.id}");
        return false;
      }

      _log.d("允许同名作品存在，跳过重复标题检查: ${updateData['name']}");

      LocalVideo.DanceCategory? newCategory;
      final categoryId = updateData['categoryId'] as String?;
      if (categoryId != null && categoryId.isNotEmpty) {
        final categoryType = DanceCategoryType.fromId(categoryId);
        if (categoryType != null) {
          newCategory = LocalVideo.DanceCategory(
            id: categoryType.id,
            name: categoryType.name,
          );
        }
      } else {
        newCategory = video.category;
      }

      LocalVideo.DanceLevel? newLevel;
      final levelName = updateData['level'] as String?;
      if (levelName != null) {
        // 使用 dance_attributes.dart 中的 DanceLevel
        final attributeLevel = Attributes.DanceLevel.fromString(levelName);
        // 转换为 local_dance_video.dart 中的 DanceLevel
        switch (attributeLevel) {
          case Attributes.DanceLevel.beginner:
            newLevel = LocalVideo.DanceLevel.level1;
            break;
          case Attributes.DanceLevel.intermediate:
            newLevel = LocalVideo.DanceLevel.level2;
            break;
          case Attributes.DanceLevel.challenging:
            newLevel = LocalVideo.DanceLevel.level3;
            break;
          case Attributes.DanceLevel.expert:
            newLevel = LocalVideo.DanceLevel.level4;
            break;
          case Attributes.DanceLevel.master:
            newLevel = LocalVideo.DanceLevel.level5;
            break;
          default:
            newLevel = video.level;
        }
      } else {
        newLevel = video.level;
      }

      LocalVideo.DanceIntensity? newIntensity;
      final intensityName = updateData['intensity'] as String?;
      if (intensityName != null) {
        // 使用 dance_attributes.dart 中的 DanceIntensity 进行解析
        final attributeIntensity = Attributes.DanceIntensity.values
            .firstWhereOrNull((e) => e.name == intensityName);
        if (attributeIntensity != null) {
          // 转换为 local_dance_video.dart 中的 DanceIntensity
          switch (attributeIntensity) {
            case Attributes.DanceIntensity.low:
              newIntensity = LocalVideo.DanceIntensity.low;
              break;
            case Attributes.DanceIntensity.medium:
              newIntensity = LocalVideo.DanceIntensity.medium;
              break;
            case Attributes.DanceIntensity.high:
              newIntensity = LocalVideo.DanceIntensity.high;
              break;
            case Attributes.DanceIntensity.intense:
              newIntensity = LocalVideo.DanceIntensity.veryHigh;
              break;
            default:
              newIntensity = video.intensity;
          }
        } else {
          newIntensity = video.intensity;
        }
      } else {
        newIntensity = video.intensity;
      }

      final isMirrorEnabled =
          updateData['isMirrorEnabled'] as bool? ?? video.isMirrorEnabled;
      final showTips = updateData['showTips'] as bool? ?? video.showTips;
      final scoreModel =
          updateData['scoreModel'] as String? ?? video.scoreModel;
      final description =
          updateData['description'] as String? ?? video.description;

      final updatedVideo = video.copyWith(
        // Assuming LocalDanceVideo has a copyWith method
        name: updateData['name'] as String? ?? video.name,
        description: description,
        category: newCategory,
        level: newLevel,
        intensity: newIntensity,
        isMirrorEnabled: isMirrorEnabled,
        showTips: showTips,
        scoreModel: scoreModel,
        coverImagePath:
            updateData['coverPath'] as String? ?? video.coverImagePath,
      );

      _log.i("✅ 更新后的本地视频scoreModel: ${updatedVideo.scoreModel}");
      await _localStorageService.saveDanceVideo(updatedVideo);

      _notifyStatisticsDataChanged('update');

      return true;
    } catch (e, stackTrace) {
      _log.e("更新作品信息失败: $e");
      return false;
    }
  }

  /// 删除作品
  Future<bool> deleteWorkItem(WorkItemData workItem) async {
    try {
      _log.i("🗑️ 开始删除作品: ${workItem.name} (ID: ${workItem.id})");

      String? originalImportPath;
      String? fileHash;

      try {
        final video = await _localStorageService.getDanceVideoById(
          workItem.id!,
        );
        if (video != null) {
          originalImportPath = video.originalImportPath;
          if (workItem.originalImportPath != null &&
              workItem.originalImportPath!.isNotEmpty) {
            final file = File(workItem.originalImportPath!);
            if (file.existsSync()) {
              fileHash = await _videoImportRecordService.calculateFileHash(
                workItem.originalImportPath!,
              );
            }
          }
        }
      } catch (e) {
        _log.w("获取视频附加信息失败: $e");
      }

      await _localStorageService.deleteDanceVideo(workItem.id!);
      _log.d("✅ 本地存储删除完成: ${workItem.id}");

      // 清理视频导入记录
      bool recordRemoved = false;
      if (workItem.videoPath != null && workItem.videoPath!.isNotEmpty) {
        final Set<String> filesToDelete = {};
        filesToDelete.add(workItem.videoPath!);

        if (workItem.videoPath!.contains('/')) {
          final baseName = workItem.videoPath!.split('/').last;
          if (baseName.contains('.')) {
            final nameWithoutExt = baseName.substring(
              0,
              baseName.lastIndexOf('.'),
            );
            filesToDelete.add(nameWithoutExt);
          }
        }
        // The original logic is complex, this is a simplified interpretation of cleaning related files.
        // It seems to build a list of partial paths to remove.
        final videoExtensions = ['.mp4', '.mov', '.m4v', '.avi', '.mkv'];
        final parentDir = workItem.videoPath!.contains('/')
            ? workItem.videoPath!.substring(
                0,
                workItem.videoPath!.lastIndexOf('/'),
              )
            : '';
        final baseName = workItem.videoPath!.split('/').last;
        final nameWithoutExt = baseName.contains('.')
            ? baseName.substring(0, baseName.lastIndexOf('.'))
            : baseName;

        for (final ext in videoExtensions) {
          final potentialFile = "$parentDir/$nameWithoutExt$ext";
          // This is actually removing from a record service, not deleting a file
          recordRemoved = await _videoImportRecordService.removeVideoRecord(
            potentialFile,
            fileHash,
          );
          if (recordRemoved) break;
        }
      }

      // If not removed by path, try by hash if available
      if (!recordRemoved && fileHash != null && fileHash.isNotEmpty) {
        recordRemoved = await _videoImportRecordService.removeVideoRecordByHash(
          fileHash,
        );
      }

      // Cleanup related files from video import service
      try {
        if (workItem.name != null && workItem.name!.isNotEmpty) {
          final nameParts = workItem.name!.split(RegExp(r'[_ ]'));
          if (nameParts.isNotEmpty) {
            await _videoImportRecordService.removeVideoRecordsByPartialMatch(
              nameParts,
            );
          }
        }
        await _videoImportRecordService.removeNonExistentVideoRecords();
        await _videoImportRecordService.saveImportedVideos();
      } catch (e) {
        _log.e("清理视频导入记录失败: $e");
      }

      // 清理会话数据
      try {
        if (Get.isRegistered<AnnotationCleanupService>()) {
          final service = Get.find<AnnotationCleanupService>();
          await service.cleanupAfterVideoDelete(
            workItem.videoPath ?? '',
            workItem.name ?? '',
            workItem.id,
          );
          _log.i("视频删除后会话数据清理完成");
        }
      } catch (e) {
        _log.w("视频删除后会话数据清理失败: $e");
      }

      _notifyStatisticsDataChanged('delete');
      _log.i("🎉 作品删除操作完全成功: ${workItem.name} (ID: ${workItem.id})");
      return true;
    } catch (e, stackTrace) {
      _log.e("❌ 删除作品\"${workItem.name}\"失败: $e");
      return false;
    }
  }

  void _notifyStatisticsDataChanged(String reason) {
    try {
      if (Get.isRegistered<WorksStatisticsService>()) {
        Get.find<WorksStatisticsService>()._notifyDataChanged(reason);
        _log.d("已通知统计服务数据变更: $reason");
      }
    } catch (e) {
      _log.w("通知统计数据变更失败: $e");
    }
  }
}
